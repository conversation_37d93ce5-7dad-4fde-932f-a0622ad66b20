<!DOCTYPE html>
<html lang="he" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TaxFree Pro - תנאי שימוש והסכמות | מערכת החזרי מס מקצועית</title>
    <meta name="description" content="תנאי שימוש והסכמות למערכת TaxFree Pro - שירותי החזר מס מקצועיים עם טופס 8832">
    <meta name="keywords" content="החזר מס, מס הכנסה, טופס 8832, יפוי כח, רשות המסים, ישראל">
    <meta name="author" content="TaxFree Pro">
    <meta property="og:title" content="TaxFree Pro - מערכת החזרי מס מקצועית">
    <meta property="og:description" content="מערכת מתקדמת לחישוב והגשת החזרי מס עם תמיכה מלאה בחקיקה הישראלית">
    <meta property="og:type" content="website">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>💰</text></svg>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Heebo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        :root {
            /* Primary Brand Colors */
            --primary-color: #667eea;
            --primary-dark: #5a67d8;
            --primary-light: #a5b4fc;
            --secondary-color: #764ba2;
            --secondary-dark: #6b46c1;
            --secondary-light: #c4b5fd;
            --accent-color: #f093fb;
            --accent-dark: #e879f9;
            --accent-light: #fdf4ff;

            /* Status Colors */
            --success-color: #10b981;
            --success-dark: #059669;
            --success-light: #d1fae5;
            --warning-color: #f59e0b;
            --warning-dark: #d97706;
            --warning-light: #fef3c7;
            --error-color: #ef4444;
            --error-dark: #dc2626;
            --error-light: #fee2e2;
            --info-color: #3b82f6;
            --info-dark: #2563eb;
            --info-light: #dbeafe;

            /* Neutral Colors */
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;

            /* Theme variables */
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --bg-tertiary: var(--gray-100);
            --text-primary: #333333;
            --text-secondary: #6b7280;
            --text-tertiary: var(--gray-400);
            --border-color: #e5e7eb;
            --shadow-color: rgba(0, 0, 0, 0.1);

            /* Spacing Scale */
            --space-1: 0.25rem;
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 1rem;
            --space-5: 1.25rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --space-10: 2.5rem;
            --space-12: 3rem;
            --space-16: 4rem;
            --space-20: 5rem;

            /* Typography Scale */
            --text-xs: 0.75rem;
            --text-sm: 0.875rem;
            --text-base: 1rem;
            --text-lg: 1.125rem;
            --text-xl: 1.25rem;
            --text-2xl: 1.5rem;
            --text-3xl: 1.875rem;
            --text-4xl: 2.25rem;
            --text-5xl: 3rem;

            /* Border Radius */
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --radius-2xl: 1.5rem;
            --radius-full: 9999px;

            /* Shadows */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

            /* Transitions */
            --transition-fast: 0.15s ease-in-out;
            --transition-normal: 0.3s ease-in-out;
            --transition-slow: 0.5s ease-in-out;

            /* Z-Index Scale */
            --z-base: 0;
            --z-dropdown: 1000;
            --z-sticky: 1020;
            --z-fixed: 1030;
            --z-modal: 1050;
            --z-tooltip: 1070;
        }

        [data-theme="dark"] {
            --bg-primary: #1a1a1a;
            --bg-secondary: #2d2d2d;
            --text-primary: #ffffff;
            --text-secondary: #a0a0a0;
            --border-color: #404040;
            --shadow-color: rgba(255, 255, 255, 0.1);
            --primary-color: #8b9aff;
            --success-color: #34d399;
            --warning-color: #fbbf24;
            --error-color: #f87171;
        }

        [data-theme="high-contrast"] {
            --bg-primary: #ffffff;
            --bg-secondary: #f0f0f0;
            --text-primary: #000000;
            --text-secondary: #333333;
            --border-color: #000000;
            --primary-color: #0000ff;
            --success-color: #008000;
            --warning-color: #ff8000;
            --error-color: #ff0000;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Heebo', 'Segoe UI', 'Arial Hebrew', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            background-attachment: fixed;
            min-height: 100vh;
            color: var(--text-primary);
            padding: 0;
            line-height: 1.7;
            scroll-behavior: smooth;
            font-size: var(--text-base);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        /* Accessibility Improvements */
        *:focus {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
        }

        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* Skip to content link */
        .skip-link {
            position: absolute;
            top: -40px;
            left: 6px;
            background: var(--primary-color);
            color: white;
            padding: 8px;
            text-decoration: none;
            border-radius: var(--radius-md);
            z-index: var(--z-tooltip);
            transition: top var(--transition-fast);
        }

        .skip-link:focus {
            top: 6px;
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            *,
            *::before,
            *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
                scroll-behavior: auto !important;
            }
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
            :root {
                --primary-color: #0000ff;
                --success-color: #008000;
                --warning-color: #ff8000;
                --error-color: #ff0000;
                --text-primary: #000000;
                --bg-primary: #ffffff;
                --border-color: #000000;
            }
        }

        .header {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            padding: 15px 0;
            box-shadow: 0 4px 30px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .logo {
            font-size: 2.5em;
            font-weight: 800;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            display: flex;
            align-items: center;
            gap: 15px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.1);
            letter-spacing: -1px;
        }

        .logo-icon {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 15px;
            padding: 12px;
            font-size: 1.2em;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            animation: logoFloat 3s ease-in-out infinite;
        }

        @keyframes logoFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
        }

        .nav-menu {
            display: flex;
            gap: 30px;
            list-style: none;
        }

        .nav-menu a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: color 0.3s;
        }

        .nav-menu a:hover {
            color: #667eea;
        }



        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }



        .calculator-section {
            background: transparent;
            padding: 20px 0;
            color: white;
        }

        .calculator-container {
            background: rgba(255, 255, 255, 0.98);
            border-radius: 25px;
            padding: 50px;
            color: #333;
            box-shadow:
                0 25px 50px rgba(0,0,0,0.15),
                0 0 0 1px rgba(255,255,255,0.1),
                inset 0 1px 0 rgba(255,255,255,0.9);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255,255,255,0.2);
            position: relative;
            overflow: hidden;
        }

        .calculator-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #667eea);
            background-size: 200% 100%;
            animation: gradientShift 3s ease infinite;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        /* Terms Navigation Styles */
        .terms-header-section {
            margin-bottom: 30px;
        }

        .terms-navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .back-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            font-size: 14px;
        }

        .back-btn:hover {
            background: linear-gradient(135deg, #495057, #343a40);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .terms-progress-info {
            background: rgba(102, 126, 234, 0.1);
            padding: 8px 15px;
            border-radius: 20px;
            color: #667eea;
            font-weight: 600;
            font-size: 14px;
        }

        .terms-progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }

        .terms-progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
            border-radius: 10px;
            transition: width 0.5s ease;
            width: 0%;
            position: relative;
        }

        .terms-progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: progressShimmer 2s infinite;
        }

        @keyframes progressShimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* Final Actions Styles */
        .final-actions {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 20px;
            padding: 40px;
            margin: 40px 0;
            border: 2px solid #e9ecef;
            box-shadow: 0 10px 30px rgba(0,0,0,0.08);
        }

        .action-summary {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }

        .summary-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #28a745, #20c997);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        }

        .summary-text h4 {
            margin: 0 0 8px 0;
            color: #2c3e50;
            font-size: 1.4em;
            font-weight: 700;
        }

        .summary-text p {
            margin: 0;
            color: #6c757d;
            font-size: 1.1em;
        }

        .action-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .secondary-btn {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
            padding: 15px 25px;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            font-size: 16px;
            min-width: 180px;
            justify-content: center;
        }

        .secondary-btn:hover {
            background: linear-gradient(135deg, #495057, #343a40);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        .proceed-btn {
            display: inline-flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px 30px;
            border: none;
            border-radius: 25px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            font-size: 18px;
            min-width: 250px;
            position: relative;
            overflow: hidden;
        }

        .proceed-btn:disabled {
            background: linear-gradient(135deg, #adb5bd, #6c757d);
            cursor: not-allowed;
            transform: none;
        }

        .proceed-btn:not(:disabled):hover {
            background: linear-gradient(135deg, #764ba2, #f093fb);
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .proceed-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .proceed-btn:not(:disabled):hover::before {
            left: 100%;
        }

        .btn-subtitle {
            font-size: 12px;
            font-weight: 400;
            opacity: 0.9;
        }

        /* Responsive Design for Actions */
        @media (max-width: 768px) {
            .action-summary {
                flex-direction: column;
                text-align: center;
            }

            .action-buttons {
                flex-direction: column;
                align-items: center;
            }

            .secondary-btn,
            .proceed-btn {
                width: 100%;
                max-width: 300px;
            }

            .final-actions {
                padding: 25px;
                margin: 20px 0;
            }
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 30px 0;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 10px;
            font-weight: 700;
            color: #2c3e50;
            font-size: 1.1em;
            position: relative;
        }

        .form-group label::after {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 0;
            width: 30px;
            height: 2px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 1px;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 16px 20px;
            border: 2px solid #e5e7eb;
            border-radius: 15px;
            font-size: 16px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
        }

        .form-group input:hover,
        .form-group select:hover {
            border-color: #9ca3af;
        }

        .checkbox-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            transition: background-color 0.3s;
        }

        .checkbox-item:hover {
            background: #e9ecef;
        }

        .checkbox-item input[type="checkbox"] {
            width: auto;
            margin-left: 10px;
        }

        .calculate-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            color: white;
            padding: 20px 60px;
            border: none;
            border-radius: 50px;
            font-size: 1.4em;
            font-weight: 800;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            width: 100%;
            margin: 40px 0;
            position: relative;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .calculate-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .calculate-btn:hover::before {
            left: 100%;
        }

        .calculate-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 20px 45px rgba(102, 126, 234, 0.4);
        }

        .calculate-btn:active {
            transform: translateY(-1px);
        }

        .result-section {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 50%, #d299c2 100%);
            padding: 50px;
            border-radius: 25px;
            margin: 40px 0;
            display: none;
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
            border: 1px solid rgba(255,255,255,0.3);
            position: relative;
            overflow: hidden;
        }

        .result-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1) 25%, transparent 25%, transparent 75%, rgba(255,255,255,0.1) 75%);
            background-size: 20px 20px;
            animation: shimmer 20s linear infinite;
        }

        @keyframes shimmer {
            0% { background-position: 0 0; }
            100% { background-position: 40px 40px; }
        }

        .result-section h3 {
            color: #2c3e50;
            font-size: 2em;
            font-weight: 800;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
            z-index: 1;
        }

        .result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 25px;
            margin: 10px 0;
            background: rgba(255,255,255,0.8);
            border-radius: 15px;
            font-size: 1.2em;
            font-weight: 600;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
            transition: all 0.3s ease;
            position: relative;
            z-index: 1;
        }

        .result-item:hover {
            transform: translateX(5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .result-item.warning {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 2px solid #f59e0b;
            color: #92400e;
            font-weight: 600;
        }

        .result-item.warning:hover {
            background: linear-gradient(135deg, #fde68a 0%, #fbbf24 100%);
            transform: translateX(3px);
        }

        .result-item:last-child {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            font-weight: 800;
            font-size: 1.5em;
            padding: 25px;
            box-shadow: 0 15px 35px rgba(39, 174, 96, 0.3);
            animation: resultPulse 2s ease-in-out infinite;
        }

        @keyframes resultPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
        }

        /* Form Section Titles */
        .form-section-title {
            color: #2c3e50;
            font-size: 1.6em;
            font-weight: 800;
            margin-bottom: 25px;
            padding: 15px 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            border-left: 5px solid #667eea;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            display: flex;
            align-items: center;
            gap: 10px;
            position: relative;
            overflow: hidden;
        }

        .form-section-title::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
        }

        /* Personal Details Step Styles */
        .step-container {
            margin-bottom: 40px;
        }

        .step-header {
            text-align: center;
            margin-bottom: 40px;
            position: relative;
        }

        .step-number {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-size: 1.8em;
            font-weight: 800;
            border-radius: 50%;
            margin-bottom: 20px;
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
            animation: stepPulse 2s ease-in-out infinite;
        }

        @keyframes stepPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .step-header h3 {
            font-size: 2.2em;
            color: #2c3e50;
            margin: 15px 0 10px 0;
            font-weight: 800;
        }

        .step-header p {
            color: #6b7280;
            font-size: 1.2em;
            margin-bottom: 0;
        }

        .personal-form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .field-error {
            color: #e74c3c;
            font-size: 0.9em;
            margin-top: 5px;
            font-weight: 600;
            display: none;
        }

        .form-group.error input,
        .form-group.error select {
            border-color: #e74c3c;
            box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
        }

        .form-group.error .field-error {
            display: block;
        }

        .privacy-notice {
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f9ff 100%);
            border: 2px solid #10b981;
            border-radius: 15px;
            padding: 20px;
            margin: 30px 0;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .privacy-icon {
            font-size: 2em;
            color: #10b981;
        }

        .privacy-text {
            color: #065f46;
            font-size: 1.1em;
            line-height: 1.5;
        }

        .continue-btn {
            background: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
            color: white;
            padding: 20px 50px;
            border: none;
            border-radius: 50px;
            font-size: 1.4em;
            font-weight: 800;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            width: 100%;
            margin: 30px 0;
            position: relative;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(16, 185, 129, 0.3);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .continue-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .continue-btn:hover::before {
            left: 100%;
        }

        .continue-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 20px 45px rgba(16, 185, 129, 0.4);
        }

        .continue-btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .back-btn {
            background: linear-gradient(135deg, #6b7280, #4b5563);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            margin-top: 15px;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: linear-gradient(135deg, #4b5563, #374151);
            transform: translateY(-1px);
        }

        /* Terms of Service Styles - Simplified */
        .terms-container {
            background: rgba(255, 255, 255, 0.98);
            border-radius: 25px;
            margin: 20px 0;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            position: relative;
            padding: 40px;
        }

        .terms-progress-bar {
            background: #e5e7eb;
            height: 8px;
            border-radius: 10px;
            margin: 20px 0 40px 0;
            overflow: hidden;
            position: relative;
        }

        .terms-progress-fill {
            background: linear-gradient(90deg, #667eea, #764ba2, #10b981);
            height: 100%;
            width: 0%;
            border-radius: 10px;
            transition: width 0.5s ease;
            position: relative;
        }

        .terms-progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .progress-text {
            text-align: center;
            margin-top: 10px;
            font-weight: 600;
            color: #667eea;
        }

        .terms-section {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 20px;
            margin: 30px 0;
            padding: 35px;
            border: 1px solid #e9ecef;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .terms-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(180deg, #667eea, #764ba2, #f093fb);
            transition: width 0.3s ease;
        }

        .terms-section:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 50px rgba(0,0,0,0.15);
            border-color: #667eea;
        }

        .terms-section:hover::before {
            width: 6px;
        }

        .terms-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }

        .terms-icon {
            font-size: 2em;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            color: white;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .terms-header h4 {
            color: #2c3e50;
            font-size: 1.5em;
            font-weight: 700;
            margin: 0;
            flex: 1;
        }

        .terms-content {
            color: #495057;
            line-height: 1.7;
            font-size: 1.05em;
        }

        .terms-content ul {
            list-style: none;
            padding: 0;
        }

        .terms-content li {
            padding: 12px 0;
            border-bottom: 1px solid #f1f3f4;
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }

        .terms-content li::before {
            content: '✓';
            color: #28a745;
            font-weight: bold;
            font-size: 1.2em;
            margin-top: 2px;
        }

        .terms-content li:last-child {
            border-bottom: none;
        }

        .legal-explanation {
            background: #e0f2fe;
            border: 2px solid #0288d1;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            position: relative;
        }

        .legal-explanation::before {
            content: '⚖️';
            position: absolute;
            top: -15px;
            right: 20px;
            background: #0288d1;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2em;
        }

        .legal-title {
            color: #01579b;
            font-weight: 800;
            font-size: 1.2em;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .legal-content {
            color: #0277bd;
            line-height: 1.7;
            font-weight: 500;
        }

        .legal-content ul {
            margin: 15px 0;
            padding-right: 25px;
        }

        .legal-content li {
            margin: 10px 0;
            position: relative;
        }

        .legal-content li::before {
            content: '📋';
            position: absolute;
            right: -25px;
            top: 0;
        }

        .expand-section {
            background: #f0f9ff;
            border: 2px dashed #0ea5e9;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .expand-section:hover {
            background: #e0f2fe;
            border-color: #0284c7;
            transform: scale(1.02);
        }

        .expand-btn {
            background: linear-gradient(135deg, #0ea5e9, #0284c7);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .expand-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(14, 165, 233, 0.3);
        }

        .terms-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e5e7eb;
        }

        .terms-icon {
            font-size: 2em;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .terms-header h4 {
            color: #2c3e50;
            font-size: 1.5em;
            font-weight: 700;
            margin: 0;
        }

        .terms-content {
            color: #374151;
            line-height: 1.6;
        }

        .terms-content ul {
            padding-right: 20px;
            margin: 15px 0;
        }

        .terms-content li {
            margin: 8px 0;
            position: relative;
        }

        .pricing-highlight {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: center;
            box-shadow: 0 10px 30px rgba(16, 185, 129, 0.3);
        }

        .pricing-main {
            font-size: 2em;
            font-weight: 800;
            margin-bottom: 15px;
        }

        .pricing-benefits {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .benefit {
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9em;
        }

        .important-note {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            color: #856404;
            font-weight: 600;
        }

        .small-text {
            font-size: 0.9em;
            color: #6b7280;
            font-style: italic;
        }

        /* Agreement Section */
        .agreement-section {
            background: linear-gradient(135deg, #e0e7ff 0%, #f0f4ff 100%);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            border: 2px solid #667eea;
        }

        .agreement-section h4 {
            color: #2c3e50;
            font-size: 1.8em;
            font-weight: 800;
            margin-bottom: 25px;
            text-align: center;
        }

        .checkbox-agreements {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .agreement-item {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .agreement-item:hover {
            background: rgba(255, 255, 255, 0.95);
            border-color: #667eea;
            transform: translateX(5px);
        }

        .agreement-item input[type="checkbox"] {
            display: none;
        }

        .checkmark {
            width: 24px;
            height: 24px;
            border: 3px solid #d1d5db;
            border-radius: 6px;
            position: relative;
            transition: all 0.3s ease;
            flex-shrink: 0;
            margin-top: 2px;
        }

        .agreement-item input[type="checkbox"]:checked + .checkmark {
            background: linear-gradient(135deg, #10b981, #059669);
            border-color: #10b981;
        }

        .agreement-item input[type="checkbox"]:checked + .checkmark::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-weight: bold;
            font-size: 16px;
        }

        /* Agreement Section Styles */
        .agreement-section {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            border: 3px solid #f59e0b;
        }

        .agreement-section h4 {
            color: #92400e;
            font-size: 1.8em;
            font-weight: 800;
            margin-bottom: 15px;
            text-align: center;
        }

        .agreement-checkboxes {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin: 30px 0;
        }

        .checkbox-item {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 15px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .checkbox-item:hover {
            background: rgba(255, 255, 255, 0.95);
            border-color: #f59e0b;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(245, 158, 11, 0.2);
        }

        .checkbox-item input[type="checkbox"] {
            width: 20px;
            height: 20px;
            margin: 0;
            cursor: pointer;
            accent-color: #f59e0b;
        }

        .checkbox-item label {
            display: flex;
            align-items: center;
            gap: 12px;
            cursor: pointer;
            font-weight: 600;
            color: #374151;
            line-height: 1.5;
            margin: 0;
        }

        .checkbox-item label i {
            color: #f59e0b;
            font-size: 1.2em;
        }

        .checkbox-item input[type="checkbox"]:checked + label {
            color: #059669;
        }

        .checkbox-item input[type="checkbox"]:checked + label i {
            color: #059669;
        }

        .agreement-note {
            display: flex;
            gap: 15px;
            padding: 20px;
            background: rgba(59, 130, 246, 0.1);
            border-radius: 15px;
            border-left: 4px solid #3b82f6;
            margin: 25px 0;
        }

        .note-icon {
            color: #3b82f6;
            font-size: 1.3em;
            margin-top: 2px;
        }

        .note-text {
            color: #1e40af;
            font-weight: 600;
            line-height: 1.5;
        }

        .signature-container {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 20px;
            padding: 40px;
            margin: 40px 0;
            text-align: center;
            box-shadow: 0 15px 40px rgba(0,0,0,0.1);
            border: 3px dashed #d1d5db;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .signature-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
            transform: translateX(-100%);
            transition: transform 0.3s ease;
        }

        .signature-container.active {
            border-color: #667eea;
            box-shadow: 0 20px 50px rgba(102, 126, 234, 0.2);
            transform: translateY(-2px);
        }

        .signature-container.active::before {
            transform: translateX(0);
        }

        #signatureCanvas {
            border: 3px solid #e5e7eb;
            border-radius: 15px;
            cursor: crosshair;
            display: block;
            margin: 0 auto;
            max-width: 100%;
            background: #ffffff;
            box-shadow: inset 0 2px 8px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }

        #signatureCanvas:hover {
            border-color: #667eea;
            box-shadow: inset 0 2px 8px rgba(0,0,0,0.05), 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .signature-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 30px;
            padding-top: 25px;
            border-top: 2px solid #e5e7eb;
            flex-wrap: wrap;
            gap: 15px;
        }

        .clear-btn {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            padding: 15px 25px;
            border: none;
            border-radius: 30px;
            font-size: 1em;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 15px rgba(239, 68, 68, 0.2);
        }

        .clear-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(239, 68, 68, 0.4);
            background: linear-gradient(135deg, #dc2626, #b91c1c);
        }

        .clear-btn:active {
            transform: translateY(-1px);
        }

        .signature-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        #signatureStatus {
            font-weight: 700;
            font-size: 1.1em;
            padding: 12px 20px;
            border-radius: 25px;
            background: rgba(239, 68, 68, 0.1);
            color: #ef4444;
            transition: all 0.3s ease;
            border: 2px solid rgba(239, 68, 68, 0.2);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        #signatureStatus.signed {
            background: rgba(16, 185, 129, 0.1);
            color: #10b981;
            border-color: rgba(16, 185, 129, 0.2);
        }

        /* Signature Instructions */
        .signature-instructions {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin: 25px 0;
            padding: 20px;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 15px;
            border: 1px solid rgba(102, 126, 234, 0.1);
            flex-wrap: wrap;
        }

        .instruction-item {
            display: flex;
            align-items: center;
            gap: 10px;
            color: #667eea;
            font-weight: 600;
            font-size: 0.95em;
        }

        .instruction-item i {
            font-size: 1.2em;
            color: #667eea;
        }

        /* Responsive signature */
        @media (max-width: 768px) {
            .signature-instructions {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .instruction-item {
                justify-content: center;
            }

            #signatureCanvas {
                width: 100%;
                height: 150px;
            }

            .signature-container {
                padding: 25px 15px;
            }

            .signature-controls {
                flex-direction: column;
                gap: 15px;
            }
        }

        .signature-details {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            text-align: right;
        }

        .signature-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .signature-meta span {
            background: #f3f4f6;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 0.9em;
            font-weight: 600;
        }

        .proceed-btn {
            background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 50%, #4c1d95 100%);
            color: white;
            padding: 20px 50px;
            border: none;
            border-radius: 50px;
            font-size: 1.4em;
            font-weight: 800;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            width: 100%;
            margin: 30px 0;
            position: relative;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(124, 58, 237, 0.3);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .proceed-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .proceed-btn:hover::before {
            left: 100%;
        }

        .proceed-btn:hover:not(:disabled) {
            transform: translateY(-3px);
            box-shadow: 0 20px 45px rgba(124, 58, 237, 0.4);
        }

        .proceed-btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* Form 8832 Styles */
        .form-8832-container {
            background: white;
            border: 3px solid #2c3e50;
            border-radius: 15px;
            margin: 25px 0;
            font-family: 'Times New Roman', serif;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .form-header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 25px;
            border-radius: 12px 12px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
        }

        .form-logo {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .israel-emblem {
            font-size: 3em;
            background: linear-gradient(135deg, #0066cc, #ffffff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .form-title h3 {
            font-size: 1.8em;
            font-weight: 800;
            margin: 0;
            color: white;
        }

        .form-title h4 {
            font-size: 1.4em;
            font-weight: 600;
            margin: 5px 0;
            color: #bdc3c7;
        }

        .form-title h5 {
            font-size: 1.2em;
            font-weight: 600;
            margin: 5px 0;
            color: #ecf0f1;
        }

        .form-number {
            text-align: left;
            font-size: 1.1em;
            font-weight: 600;
        }

        .form-number span {
            display: block;
            margin: 5px 0;
        }

        .form-content {
            padding: 30px;
        }

        .form-section-title {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 15px 20px;
            margin: 25px -10px 20px -10px;
            font-size: 1.3em;
            font-weight: 700;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }

        .form-fields {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .form-field {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .form-field label {
            font-weight: 700;
            color: #2c3e50;
            font-size: 1.1em;
        }

        .field-value {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 12px 15px;
            font-size: 1.1em;
            font-weight: 600;
            color: #495057;
            min-height: 20px;
        }

        .authorization-scope {
            background: #e8f5e8;
            border: 2px solid #28a745;
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
        }

        .scope-items {
            margin: 20px 0;
        }

        .scope-item {
            display: flex;
            gap: 15px;
            margin: 15px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 10px;
            border-left: 4px solid #28a745;
        }

        .scope-number {
            background: #28a745;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 800;
            flex-shrink: 0;
        }

        .scope-content {
            line-height: 1.6;
            color: #2c3e50;
        }

        .limitations {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
        }

        .limitation-item {
            display: flex;
            gap: 15px;
            margin: 15px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 10px;
            border-left: 4px solid #ffc107;
        }

        .limitation-icon {
            font-size: 1.5em;
            flex-shrink: 0;
        }

        .limitation-text {
            line-height: 1.6;
            color: #856404;
            font-weight: 600;
        }

        .validity-section {
            background: #e7f3ff;
            border: 2px solid #007bff;
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
        }

        .validity-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 15px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 10px;
            border-left: 4px solid #007bff;
        }

        .validity-item label {
            font-weight: 700;
            color: #2c3e50;
        }

        .validity-date {
            font-weight: 600;
            color: #007bff;
        }

        .form-footer {
            border-top: 3px solid #2c3e50;
            padding-top: 25px;
            margin-top: 30px;
        }

        .signature-area {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
        }

        .signature-field {
            text-align: center;
        }

        .signature-field label {
            display: block;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .signature-line {
            display: block;
            border-bottom: 2px solid #2c3e50;
            min-height: 40px;
            padding: 10px;
            font-weight: 600;
            color: #495057;
        }

        /* Power of Attorney Final Section */
        .power-of-attorney-final-section {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 3px solid #f59e0b;
            border-radius: 20px;
            padding: 30px;
            margin: 40px 0;
            position: relative;
        }

        .power-of-attorney-final-section::before {
            content: '📜';
            position: absolute;
            top: -20px;
            right: 30px;
            background: #f59e0b;
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5em;
            box-shadow: 0 5px 15px rgba(245, 158, 11, 0.3);
        }

        .power-of-attorney-final-section h4 {
            color: #92400e;
            font-size: 1.8em;
            font-weight: 800;
            margin-bottom: 20px;
            text-align: center;
        }

        /* Important note and form actions */
        .important-note {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 2px solid #f59e0b;
            border-radius: 15px;
            padding: 20px;
            margin: 25px 0;
            color: #92400e;
            font-weight: 600;
            line-height: 1.6;
            position: relative;
        }

        .important-note::before {
            content: '⚠️';
            position: absolute;
            top: -15px;
            right: 20px;
            background: #f59e0b;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2em;
        }

        .form-actions {
            text-align: center;
            margin: 25px 0;
        }

        .form-actions .expand-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-weight: 700;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .form-actions .expand-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        /* Form signature styles */
        .signature-field-canvas {
            display: flex;
            flex-direction: column;
            gap: 10px;
            width: 100%;
        }

        .signature-field-canvas label {
            font-weight: 700;
            color: #2c3e50;
            font-size: 1.1em;
        }



        .signature-instruction {
            text-align: center;
            margin-top: 10px;
        }

        .instruction-title {
            color: #2c3e50;
            font-weight: bold;
            font-size: 1em;
            margin-bottom: 8px;
        }

        .instruction-details {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .instruction-details span {
            color: #6b7280;
            font-size: 0.85em;
            background: rgba(102, 126, 234, 0.1);
            padding: 4px 8px;
            border-radius: 8px;
            border: 1px solid rgba(102, 126, 234, 0.2);
        }

        /* ULTRA SIGNATURE STYLES */
        .ultra-signature-container {
            margin: 30px 0;
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 24px;
            box-shadow: 0 12px 48px rgba(0, 0, 0, 0.08);
            border: 2px solid rgba(226, 232, 240, 0.6);
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }

        .ultra-signature-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #667eea 100%);
            background-size: 200% 100%;
            animation: shimmer 3s ease-in-out infinite;
        }

        @keyframes shimmer {
            0%, 100% { background-position: 200% 0; }
            50% { background-position: -200% 0; }
        }

        .ultra-signature-container:hover {
            box-shadow: 0 16px 56px rgba(0, 0, 0, 0.12);
            transform: translateY(-3px);
            border-color: rgba(102, 126, 234, 0.3);
        }

        /* Premium Header */
        .signature-header-premium {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            padding: 25px 30px;
            position: relative;
            overflow: hidden;
        }

        .signature-header-premium::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%, rgba(255,255,255,0.1) 100%);
            background-size: 200% 200%;
            animation: headerShine 4s ease-in-out infinite;
        }

        @keyframes headerShine {
            0%, 100% { background-position: 200% 200%; }
            50% { background-position: -200% -200%; }
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            position: relative;
            z-index: 2;
            color: white;
        }

        .signature-title-premium {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .title-icon-wrapper {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .signature-icon-premium {
            font-size: 1.8em;
            color: white;
            animation: iconPulse 2s ease-in-out infinite;
        }

        @keyframes iconPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .title-text h3 {
            font-size: 1.4em;
            font-weight: 800;
            margin: 0 0 5px 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .title-text p {
            font-size: 0.95em;
            margin: 0;
            opacity: 0.9;
            font-weight: 500;
        }

        .signature-date-premium {
            display: flex;
            align-items: center;
        }

        .date-wrapper {
            background: rgba(255, 255, 255, 0.15);
            padding: 12px 20px;
            border-radius: 30px;
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .date-icon-premium {
            font-size: 1.2em;
            color: rgba(255, 255, 255, 0.9);
        }

        .date-info {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .date-label-premium {
            font-size: 0.8em;
            font-weight: 600;
            opacity: 0.8;
        }

        .date-value-premium {
            font-size: 1.1em;
            font-weight: 800;
            background: rgba(255, 255, 255, 0.25);
            padding: 4px 12px;
            border-radius: 20px;
            min-width: 90px;
            text-align: center;
        }

        /* Clean Workspace */
        .signature-workspace-clean {
            padding: 30px;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        }

        .signature-instructions-clean {
            background: #ffffff;
            border: 2px solid #e2e8f0;
            border-radius: 16px;
            padding: 20px;
            margin-bottom: 25px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.04);
        }

        .instruction-header {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #e2e8f0;
        }

        .instruction-icon {
            color: #667eea;
            font-size: 1.3em;
        }

        .instruction-title {
            font-size: 1.1em;
            font-weight: 700;
            color: #1e293b;
        }

        .instruction-methods {
            display: flex;
            justify-content: center;
            gap: 30px;
        }

        .method-item {
            display: flex;
            align-items: center;
            gap: 10px;
            color: #475569;
            font-weight: 600;
            font-size: 0.95em;
        }

        .method-item i {
            color: #667eea;
            font-size: 1.2em;
            width: 20px;
            text-align: center;
        }

        .signature-pad-clean {
            background: #ffffff;
            border-radius: 16px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
            border: 2px solid #e2e8f0;
            overflow: hidden;
            transition: all 0.3s ease;
            position: relative;
        }

        .signature-pad-clean:hover {
            border-color: #cbd5e1;
            box-shadow: 0 6px 24px rgba(0, 0, 0, 0.08);
            transform: translateY(-1px);
        }

        .signature-area-clean {
            position: relative;
            background: #ffffff;
            padding: 25px;
            min-height: 180px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .signature-lines-clean {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            z-index: 1;
        }

        .line-clean {
            position: absolute;
            left: 30px;
            right: 30px;
            height: 1px;
            background: linear-gradient(to right,
                transparent 0%,
                #e2e8f0 20%,
                #cbd5e1 50%,
                #e2e8f0 80%,
                transparent 100%);
        }

        .line-top-clean {
            top: 35%;
            opacity: 0.6;
        }

        .line-main-clean {
            top: 65%;
            height: 2px;
            background: linear-gradient(to right,
                transparent 0%,
                #cbd5e1 20%,
                #94a3b8 50%,
                #cbd5e1 80%,
                transparent 100%);
        }

        .line-bottom-clean {
            bottom: 25%;
            opacity: 0.4;
        }

        /* Premium Guidelines */
        .signature-lines {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            z-index: 1;
        }

        .signature-line {
            position: absolute;
            left: 35px;
            right: 35px;
            height: 1px;
            background: linear-gradient(to right,
                transparent 0%,
                rgba(102, 126, 234, 0.2) 10%,
                rgba(102, 126, 234, 0.4) 50%,
                rgba(102, 126, 234, 0.2) 90%,
                transparent 100%);
        }

        .line-top {
            top: 30%;
            opacity: 0.6;
        }

        .line-main {
            top: 60%;
            height: 2px;
            background: linear-gradient(to right,
                transparent 0%,
                rgba(102, 126, 234, 0.3) 10%,
                rgba(102, 126, 234, 0.6) 50%,
                rgba(102, 126, 234, 0.3) 90%,
                transparent 100%);
        }

        .line-bottom {
            bottom: 20%;
            opacity: 0.4;
        }

        /* Clean Canvas */
        .signature-canvas-clean {
            display: block;
            cursor: crosshair;
            background: transparent;
            border: none;
            position: relative;
            z-index: 3;
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .signature-canvas-clean:hover {
            filter: brightness(1.01);
        }

        .signature-canvas-clean:active {
            filter: brightness(1.02);
        }

        /* Clean Prompt */
        .signature-prompt-clean {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            pointer-events: none;
            transition: all 0.4s ease;
            z-index: 2;
        }

        .signature-prompt-clean.hidden {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0.9);
        }

        .prompt-content-clean {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 12px;
            background: rgba(255, 255, 255, 0.95);
            padding: 20px 25px;
            border-radius: 16px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(226, 232, 240, 0.8);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
        }

        .prompt-icon-clean {
            font-size: 2.2em;
            color: #667eea;
            animation: cleanFloat 2.5s ease-in-out infinite;
        }

        @keyframes cleanFloat {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-6px);
            }
        }

        .prompt-text-clean {
            text-align: center;
        }

        .prompt-main-clean {
            font-size: 1.2em;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 4px;
        }

        .prompt-sub-clean {
            font-size: 0.9em;
            color: #64748b;
            font-weight: 500;
        }

        /* Clean Controls */
        .signature-controls-clean {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 25px;
            padding: 20px 25px;
            background: #ffffff;
            border-radius: 16px;
            border: 2px solid #e2e8f0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        }

        .control-left-clean, .control-right-clean {
            display: flex;
            align-items: center;
        }

        .clear-btn-clean {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 12px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.95em;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 12px rgba(245, 158, 11, 0.25);
        }

        .clear-btn-clean:hover {
            background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(245, 158, 11, 0.35);
        }

        .clear-btn-clean:active {
            transform: translateY(0);
        }

        /* Clean Status */
        .status-container-clean {
            display: flex;
            align-items: center;
        }

        .status-indicator-clean {
            display: flex;
            align-items: center;
            gap: 10px;
            background: #f8fafc;
            padding: 10px 16px;
            border-radius: 20px;
            border: 1px solid #e2e8f0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
            transition: all 0.3s ease;
        }

        .status-icon-clean {
            font-size: 1em;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
        }

        .status-icon-clean.waiting {
            color: #94a3b8;
            animation: cleanPulse 2s infinite;
        }

        .status-icon-clean.signing {
            color: #3b82f6;
            animation: cleanSpin 1s linear infinite;
        }

        .status-icon-clean.signed {
            color: #10b981;
            animation: cleanBounce 0.5s ease-out;
        }

        .status-text-clean {
            display: flex;
            flex-direction: column;
        }

        .status-main-clean {
            font-weight: 600;
            font-size: 0.95em;
            transition: all 0.3s ease;
        }

        .status-main-clean.waiting {
            color: #64748b;
        }

        .status-main-clean.signing {
            color: #3b82f6;
        }

        .status-main-clean.signed {
            color: #10b981;
        }

        @keyframes cleanPulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.5;
            }
        }

        @keyframes cleanSpin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        @keyframes cleanBounce {
            0% { transform: scale(0.9); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }

        /* Active States */
        .signature-pad.active {
            border-color: #3b82f6;
            box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.06), 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .signature-pad.signed {
            border-color: #10b981;
            box-shadow: inset 0 2px 8px rgba(0, 0, 0, 0.06), 0 0 0 3px rgba(16, 185, 129, 0.1);
        }

        .signature-background.active {
            background: linear-gradient(145deg, #ffffff 0%, #f0f9ff 100%);
        }

        .signature-background.signed {
            background: linear-gradient(145deg, #ffffff 0%, #f0fdf4 100%);
        }

        /* Date Enhancement */
        .date-value-premium.signed {
            background: rgba(16, 185, 129, 0.3);
            color: #065f46;
            font-weight: 900;
            box-shadow: 0 2px 8px rgba(16, 185, 129, 0.2);
        }

        /* PROFESSIONAL WEBSITE STYLES */

        /* Beautiful Professional Header */
        .professional-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%);
            padding: 30px 0;
            box-shadow: 0 10px 40px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }

        .professional-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="20" cy="80" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .container-professional {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 30px;
        }

        .header-content-professional {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: white;
        }

        .logo-section-professional {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .logo-icon-professional {
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2em;
            border: 2px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 2;
        }

        .logo-text-professional h1 {
            font-size: 2.2em;
            font-weight: 900;
            margin: 0 0 5px 0;
            text-shadow: 0 3px 6px rgba(0,0,0,0.3);
            letter-spacing: -0.5px;
        }

        .logo-text-professional p {
            font-size: 1em;
            margin: 0 0 8px 0;
            opacity: 0.95;
            font-weight: 600;
        }

        .license-info {
            margin-top: 5px;
        }

        .license-badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: 700;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .credentials-section {
            display: flex;
            gap: 30px;
        }

        .credential-item {
            display: flex;
            align-items: center;
            gap: 15px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
            padding: 18px 25px;
            border-radius: 16px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(15px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
            z-index: 2;
        }

        .credential-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
        }

        .credential-item i {
            font-size: 1.4em;
            opacity: 0.9;
        }

        .credential-text {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .credential-title {
            font-size: 0.85em;
            opacity: 0.8;
            font-weight: 500;
        }

        .credential-detail {
            font-size: 0.95em;
            font-weight: 700;
        }

        /* Clean Progress */
        .progress-container-clean {
            background: #ffffff;
            border-bottom: 1px solid #e2e8f0;
            padding: 20px 0;
        }

        .progress-steps-clean {
            display: flex;
            justify-content: center;
            gap: 40px;
            max-width: 800px;
            margin: 0 auto;
        }

        .step-clean {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 15px 20px;
            border-radius: 12px;
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            transition: all 0.3s ease;
            min-width: 180px;
        }

        .step-clean.completed {
            background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
            border-color: #10b981;
            color: #065f46;
        }

        .step-clean.active {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            border-color: #3b82f6;
            color: #1e40af;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
        }

        .step-number {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 0.9em;
        }

        .step-clean.completed .step-number {
            background: #10b981;
            color: white;
        }

        .step-clean.active .step-number {
            background: #3b82f6;
            color: white;
        }

        .step-info {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .step-title {
            font-weight: 700;
            font-size: 0.95em;
        }

        .step-status {
            font-size: 0.8em;
            opacity: 0.8;
            font-weight: 500;
        }

        /* Beautiful Professional Welcome Section */
        .welcome-section-professional {
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 50%, #f1f5f9 100%);
            padding: 60px 0;
            position: relative;
            overflow: hidden;
        }

        .welcome-section-professional::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20% 80%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(118, 75, 162, 0.1) 0%, transparent 50%);
            pointer-events: none;
        }

        .welcome-content-professional {
            max-width: 1000px;
            margin: 0 auto;
            text-align: center;
        }

        .legal-header {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 25px;
            margin-bottom: 40px;
        }

        .legal-seal {
            width: 90px;
            height: 90px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.8em;
            color: white;
            box-shadow: 0 12px 40px rgba(102, 126, 234, 0.4);
            border: 4px solid rgba(255, 255, 255, 0.3);
            position: relative;
            animation: gentlePulse 3s ease-in-out infinite;
        }

        @keyframes gentlePulse {
            0%, 100% {
                transform: scale(1);
                box-shadow: 0 12px 40px rgba(102, 126, 234, 0.4);
            }
            50% {
                transform: scale(1.05);
                box-shadow: 0 16px 50px rgba(102, 126, 234, 0.5);
            }
        }

        .legal-title-section {
            text-align: right;
        }

        .main-title-professional {
            font-size: 2.5em;
            font-weight: 900;
            color: #1e293b;
            margin: 0 0 10px 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
            letter-spacing: -0.5px;
        }

        .legal-subtitle {
            font-size: 1.1em;
            color: #64748b;
            margin: 0;
            font-weight: 600;
            font-style: italic;
        }

        .legal-notice-box {
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 50%, #dbeafe 100%);
            border: 2px solid #3b82f6;
            border-radius: 20px;
            padding: 30px;
            margin: 40px 0;
            display: flex;
            align-items: flex-start;
            gap: 25px;
            text-align: right;
            box-shadow: 0 8px 32px rgba(59, 130, 246, 0.2);
            position: relative;
            overflow: hidden;
        }

        .legal-notice-box::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 0%, rgba(255, 255, 255, 0.1) 50%, transparent 100%);
            pointer-events: none;
        }

        .notice-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.8em;
            color: white;
            flex-shrink: 0;
            box-shadow: 0 6px 20px rgba(59, 130, 246, 0.3);
            border: 3px solid rgba(255, 255, 255, 0.3);
            position: relative;
            z-index: 2;
        }

        .notice-content h3 {
            font-size: 1.4em;
            font-weight: 800;
            color: #1e40af;
            margin: 0 0 12px 0;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        .notice-content p {
            font-size: 1.05em;
            color: #1e40af;
            margin: 0;
            line-height: 1.7;
            font-weight: 600;
        }

        .client-info-professional {
            display: inline-flex;
            align-items: center;
            gap: 12px;
            background: #ffffff;
            padding: 15px 25px;
            border-radius: 30px;
            border: 2px solid #1e3a8a;
            color: #1e3a8a;
            font-weight: 700;
            box-shadow: 0 6px 20px rgba(30, 58, 138, 0.15);
            margin-top: 20px;
        }

        .client-info-professional i {
            color: #1e3a8a;
            font-size: 1.2em;
        }

        /* Help Button Clean */
        .help-button-clean {
            position: fixed;
            bottom: 30px;
            left: 30px;
            z-index: 1000;
        }

        .help-btn-clean {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
        }

        .help-btn-clean:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        /* Beautiful Professional Terms Sections */
        .terms-section-professional {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border: 2px solid #e2e8f0;
            border-radius: 20px;
            margin: 40px 0;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            position: relative;
        }

        .terms-section-professional:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
            border-color: #cbd5e1;
        }

        .terms-section-professional::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #667eea 100%);
        }

        .terms-header-professional {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%);
            color: white;
            padding: 25px 35px;
            display: flex;
            align-items: center;
            gap: 25px;
            position: relative;
            overflow: hidden;
        }

        .terms-header-professional::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 0%, rgba(255, 255, 255, 0.1) 50%, transparent 100%);
            pointer-events: none;
        }

        .section-number {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.15) 100%);
            padding: 10px 20px;
            border-radius: 25px;
            font-weight: 900;
            font-size: 1.2em;
            border: 2px solid rgba(255, 255, 255, 0.4);
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 2;
        }

        .terms-icon-professional {
            font-size: 1.5em;
        }

        .terms-header-professional h4 {
            font-size: 1.4em;
            font-weight: 800;
            margin: 0;
            flex: 1;
        }

        .terms-content-professional {
            padding: 30px;
        }

        .legal-framework {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            border: 2px solid #e2e8f0;
            border-radius: 16px;
            padding: 30px;
            margin-bottom: 30px;
            position: relative;
            overflow: hidden;
        }

        .legal-framework::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(180deg, #667eea 0%, #764ba2 100%);
        }

        .legal-framework h5 {
            color: #1e3a8a;
            font-weight: 800;
            margin: 0 0 15px 0;
            font-size: 1.1em;
        }

        .legal-references {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .legal-ref {
            display: flex;
            align-items: flex-start;
            gap: 18px;
            padding: 18px;
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 12px;
            border: 2px solid #e2e8f0;
            transition: all 0.3s ease;
            position: relative;
        }

        .legal-ref:hover {
            border-color: #cbd5e1;
            transform: translateX(5px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
        }

        .ref-number {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 8px 12px;
            border-radius: 10px;
            font-weight: 800;
            font-size: 0.9em;
            min-width: 35px;
            text-align: center;
            box-shadow: 0 3px 10px rgba(102, 126, 234, 0.3);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .ref-text {
            font-size: 0.95em;
            line-height: 1.5;
            color: #374151;
        }

        .service-definition, .pricing-structure {
            margin: 25px 0;
        }

        .service-definition h5, .pricing-structure h5 {
            color: #1e3a8a;
            font-weight: 800;
            margin: 0 0 20px 0;
            font-size: 1.1em;
        }

        .service-items {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .service-item {
            display: flex;
            align-items: flex-start;
            gap: 20px;
            padding: 20px;
            background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
            border-radius: 14px;
            border-right: 5px solid #667eea;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .service-item:hover {
            transform: translateX(5px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
            border-right-color: #764ba2;
        }

        .item-number {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 10px 16px;
            border-radius: 12px;
            font-weight: 900;
            min-width: 30px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
            border: 2px solid rgba(255, 255, 255, 0.2);
            font-size: 1.1em;
        }

        .item-content {
            font-size: 0.95em;
            line-height: 1.5;
            color: #374151;
        }

        .professional-standards {
            margin: 25px 0;
        }

        .standards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .standard-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px 20px;
            background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 50%, #dcfce7 100%);
            border-radius: 14px;
            border: 2px solid #10b981;
            color: #065f46;
            font-weight: 700;
            font-size: 0.95em;
            transition: all 0.3s ease;
            box-shadow: 0 3px 12px rgba(16, 185, 129, 0.2);
        }

        .standard-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(16, 185, 129, 0.3);
            border-color: #059669;
        }

        .standard-item i {
            color: #10b981;
            font-size: 1.1em;
        }

        /* Professional Pricing Styles */
        .pricing-main-professional {
            display: flex;
            align-items: center;
            gap: 30px;
            background: linear-gradient(135deg, #10b981 0%, #059669 50%, #10b981 100%);
            color: white;
            padding: 35px;
            border-radius: 20px;
            margin: 25px 0;
            box-shadow: 0 12px 40px rgba(16, 185, 129, 0.4);
            position: relative;
            overflow: hidden;
        }

        .pricing-main-professional::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 0%, rgba(255, 255, 255, 0.1) 50%, transparent 100%);
            pointer-events: none;
        }

        .pricing-percentage {
            font-size: 3.5em;
            font-weight: 900;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .pricing-description {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .pricing-title {
            font-size: 1.3em;
            font-weight: 800;
        }

        .pricing-subtitle {
            font-size: 1em;
            opacity: 0.9;
            font-weight: 500;
        }

        .pricing-conditions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 12px;
            margin: 20px 0;
        }

        .condition-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 15px;
            background: #f8fafc;
            border-radius: 10px;
            border: 1px solid #e2e8f0;
            font-size: 0.95em;
            font-weight: 600;
        }

        .condition-item i.fa-check-circle {
            color: #10b981;
            font-size: 1.1em;
        }

        .condition-item i.fa-times-circle {
            color: #ef4444;
            font-size: 1.1em;
        }

        .calculation-examples {
            margin: 25px 0;
        }

        .examples-table {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border: 2px solid #e2e8f0;
            border-radius: 16px;
            overflow: hidden;
            margin-top: 20px;
            box-shadow: 0 6px 24px rgba(0, 0, 0, 0.08);
        }

        .example-row {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 20px;
            padding: 15px 20px;
            font-weight: 600;
            text-align: center;
        }

        .example-row.header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: 900;
            font-size: 1em;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }

        .example-row:not(.header) {
            border-bottom: 1px solid #e2e8f0;
            font-size: 0.95em;
        }

        .example-row:not(.header):last-child {
            border-bottom: none;
        }

        .example-row:not(.header):nth-child(even) {
            background: #f8fafc;
        }

        .legal-timelines {
            margin: 25px 0;
        }

        .timeline-items {
            display: flex;
            flex-direction: column;
            gap: 12px;
            margin-top: 15px;
        }

        .timeline-item {
            display: flex;
            align-items: center;
            gap: 20px;
            padding: 15px;
            background: #f8fafc;
            border-radius: 10px;
            border-right: 4px solid #f59e0b;
        }

        .timeline-period {
            background: #f59e0b;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 800;
            font-size: 0.9em;
            min-width: 100px;
            text-align: center;
        }

        .timeline-desc {
            font-size: 0.95em;
            color: #374151;
            font-weight: 600;
        }

        /* Power of Attorney Professional Styles */
        .power-of-attorney-scope {
            margin: 25px 0;
        }

        .scope-items {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin-top: 15px;
        }

        .scope-item {
            padding: 20px;
            border-radius: 12px;
            border: 2px solid;
            display: flex;
            align-items: flex-start;
            gap: 15px;
        }

        .scope-item.authorized {
            background: linear-gradient(135deg, #dcfce7 0%, #bbf7d0 100%);
            border-color: #10b981;
        }

        .scope-item.limited {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border-color: #0ea5e9;
        }

        .scope-item i {
            font-size: 1.5em;
            margin-top: 2px;
        }

        .scope-item.authorized i {
            color: #10b981;
        }

        .scope-item.limited i {
            color: #0ea5e9;
        }

        .scope-content {
            flex: 1;
        }

        .scope-content strong {
            display: block;
            margin-bottom: 10px;
            font-size: 1.1em;
            font-weight: 800;
        }

        .scope-item.authorized .scope-content strong {
            color: #065f46;
        }

        .scope-item.limited .scope-content strong {
            color: #0c4a6e;
        }

        .scope-content ul {
            margin: 0;
            padding-right: 20px;
            list-style-type: disc;
        }

        .scope-content li {
            margin: 5px 0;
            font-size: 0.95em;
            line-height: 1.4;
        }

        .scope-item.authorized .scope-content li {
            color: #065f46;
        }

        .scope-item.limited .scope-content li {
            color: #0c4a6e;
        }

        .validity-terms {
            margin: 25px 0;
        }

        .validity-items {
            display: flex;
            flex-direction: column;
            gap: 12px;
            margin-top: 15px;
        }

        .validity-item {
            display: flex;
            align-items: center;
            gap: 20px;
            padding: 15px;
            background: #f8fafc;
            border-radius: 10px;
            border-right: 4px solid #6366f1;
        }

        .validity-period {
            background: #6366f1;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 800;
            font-size: 0.9em;
            min-width: 80px;
            text-align: center;
        }

        .validity-desc {
            font-size: 0.95em;
            color: #374151;
            font-weight: 600;
        }

        .form-preview-section {
            margin: 25px 0;
            padding: 20px;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border: 2px solid #0ea5e9;
            border-radius: 12px;
        }

        .expand-btn-professional {
            background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 10px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 1em;
            margin-top: 10px;
            box-shadow: 0 4px 12px rgba(14, 165, 233, 0.3);
        }

        .expand-btn-professional:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(14, 165, 233, 0.4);
        }

        /* Privacy & Compliance Professional Styles */
        .privacy-protection {
            margin: 25px 0;
        }

        .protection-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 15px;
        }

        .protection-item {
            background: #ffffff;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .protection-item:hover {
            border-color: #3b82f6;
            transform: translateY(-2px);
            box-shadow: 0 8px 24px rgba(59, 130, 246, 0.15);
        }

        .protection-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px auto;
            font-size: 1.5em;
            color: white;
        }

        .protection-content h6 {
            font-size: 1.1em;
            font-weight: 800;
            color: #1e293b;
            margin: 0 0 8px 0;
        }

        .protection-content p {
            font-size: 0.9em;
            color: #64748b;
            margin: 0;
            line-height: 1.4;
        }

        .regulatory-compliance {
            margin: 25px 0;
        }

        .compliance-items {
            display: flex;
            flex-direction: column;
            gap: 15px;
            margin-top: 15px;
        }

        .compliance-item {
            display: flex;
            align-items: center;
            gap: 20px;
            padding: 20px;
            background: #f8fafc;
            border-radius: 12px;
            border-right: 4px solid #10b981;
        }

        .compliance-badge {
            background: #10b981;
            color: white;
            padding: 10px 16px;
            border-radius: 20px;
            font-weight: 800;
            font-size: 0.9em;
            min-width: 80px;
            text-align: center;
        }

        .compliance-details {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .compliance-details strong {
            font-size: 1em;
            font-weight: 800;
            color: #1e293b;
        }

        .compliance-details span {
            font-size: 0.9em;
            color: #64748b;
            font-weight: 500;
        }

        .client-rights {
            margin: 25px 0;
        }

        .rights-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 12px;
            margin-top: 15px;
        }

        .right-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 15px;
            background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
            border-radius: 10px;
            border: 1px solid #3b82f6;
            color: #1e40af;
            font-weight: 600;
            font-size: 0.9em;
        }

        .right-item i {
            color: #3b82f6;
            font-size: 1.1em;
        }

        /* Beautiful Professional Summary Styles */
        .professional-summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%);
            color: white;
            border-radius: 20px;
            margin: 50px 0;
            overflow: hidden;
            box-shadow: 0 12px 48px rgba(102, 126, 234, 0.4);
            position: relative;
        }

        .professional-summary::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 0%, rgba(255, 255, 255, 0.1) 50%, transparent 100%);
            pointer-events: none;
        }

        .summary-header {
            display: flex;
            align-items: center;
            gap: 20px;
            padding: 25px 30px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .summary-icon {
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2em;
            border: 3px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
            position: relative;
            z-index: 2;
        }

        .summary-content h3 {
            font-size: 1.5em;
            font-weight: 800;
            margin: 0 0 8px 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .summary-content p {
            font-size: 1em;
            margin: 0;
            opacity: 0.9;
            line-height: 1.5;
        }

        .legal-declaration {
            padding: 30px 35px;
            background: rgba(255, 255, 255, 0.08);
            position: relative;
            z-index: 2;
        }

        .declaration-text {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
            padding: 25px;
            border-radius: 16px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .declaration-text p {
            font-size: 1em;
            line-height: 1.6;
            margin: 0;
            font-weight: 600;
        }

        .declaration-text strong {
            color: #fbbf24;
            font-weight: 800;
        }

        /* Beautiful Service Overview Cards */
        .service-overview-cards {
            margin: 40px 0;
        }

        .overview-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .overview-header h3 {
            font-size: 2em;
            font-weight: 800;
            color: #1e293b;
            margin: 0 0 10px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .overview-header p {
            font-size: 1.1em;
            color: #64748b;
            margin: 0;
            font-weight: 600;
        }

        .service-cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .service-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border: 2px solid #e2e8f0;
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            position: relative;
            overflow: hidden;
        }

        .service-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, transparent 0%, rgba(102, 126, 234, 0.05) 100%);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .service-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
            border-color: #cbd5e1;
        }

        .service-card:hover::before {
            opacity: 1;
        }

        .card-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 20px auto;
            font-size: 2em;
            color: white;
            box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
            position: relative;
            z-index: 2;
        }

        .card-content h4 {
            font-size: 1.3em;
            font-weight: 800;
            color: #1e293b;
            margin: 0 0 10px 0;
        }

        .card-content p {
            font-size: 1em;
            color: #64748b;
            margin: 0;
            line-height: 1.5;
            font-weight: 500;
        }

        .legal-basis-summary {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border: 2px solid #0ea5e9;
            border-radius: 20px;
            padding: 30px;
            display: flex;
            align-items: center;
            gap: 25px;
            text-align: right;
        }

        .basis-icon {
            width: 70px;
            height: 70px;
            background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2em;
            color: white;
            flex-shrink: 0;
            box-shadow: 0 6px 20px rgba(14, 165, 233, 0.3);
        }

        .basis-content h4 {
            font-size: 1.4em;
            font-weight: 800;
            color: #0c4a6e;
            margin: 0 0 8px 0;
        }

        .basis-content p {
            font-size: 1em;
            color: #0c4a6e;
            margin: 0;
            line-height: 1.6;
            font-weight: 600;
        }

        /* Beautiful Pricing Overview */
        .pricing-overview-beautiful {
            margin: 40px 0;
        }

        .pricing-header {
            text-align: center;
            margin-bottom: 40px;
        }

        .pricing-header h3 {
            font-size: 2em;
            font-weight: 800;
            color: #1e293b;
            margin: 0 0 10px 0;
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .pricing-header p {
            font-size: 1.1em;
            color: #64748b;
            margin: 0;
            font-weight: 600;
        }

        .pricing-main-card {
            background: linear-gradient(135deg, #10b981 0%, #059669 50%, #10b981 100%);
            color: white;
            border-radius: 24px;
            padding: 40px;
            margin-bottom: 40px;
            box-shadow: 0 16px 48px rgba(16, 185, 129, 0.4);
            position: relative;
            overflow: hidden;
        }

        .pricing-main-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 0%, rgba(255, 255, 255, 0.1) 50%, transparent 100%);
            pointer-events: none;
        }

        .pricing-badge {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 30px;
            margin-bottom: 30px;
            position: relative;
            z-index: 2;
        }

        .percentage-large {
            font-size: 4em;
            font-weight: 900;
            text-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .percentage-text {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .percentage-text span:first-child {
            font-size: 1.4em;
            font-weight: 800;
        }

        .percentage-text span:last-child {
            font-size: 1.1em;
            opacity: 0.9;
            font-weight: 600;
        }

        .pricing-benefits-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            position: relative;
            z-index: 2;
        }

        .benefit-item {
            display: flex;
            align-items: center;
            gap: 12px;
            background: rgba(255, 255, 255, 0.15);
            padding: 15px 20px;
            border-radius: 16px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            font-weight: 700;
        }

        .benefit-item i {
            font-size: 1.3em;
            opacity: 0.9;
        }

        .pricing-examples-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .example-card {
            background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
            border: 2px solid #e2e8f0;
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .example-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
            border-color: #cbd5e1;
        }

        .example-card.special {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border-color: #f59e0b;
        }

        .example-amount {
            font-size: 2em;
            font-weight: 900;
            color: #1e293b;
            margin-bottom: 5px;
        }

        .example-card.special .example-amount {
            color: #92400e;
        }

        .example-label {
            font-size: 0.9em;
            color: #64748b;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .example-card.special .example-label {
            color: #92400e;
        }

        .example-arrow {
            font-size: 1.5em;
            color: #10b981;
            margin-bottom: 15px;
        }

        .example-card.special .example-arrow {
            color: #f59e0b;
        }

        .example-fee {
            font-size: 1.5em;
            font-weight: 800;
            color: #10b981;
            margin-bottom: 5px;
        }

        .example-card.special .example-fee {
            color: #f59e0b;
        }

        .example-fee-label {
            font-size: 0.85em;
            color: #64748b;
            font-weight: 600;
        }

        .example-card.special .example-fee-label {
            color: #92400e;
        }

        .timeline-summary {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            border: 2px solid #0ea5e9;
            border-radius: 20px;
            padding: 30px;
        }

        .timeline-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
        }

        .timeline-header i {
            font-size: 1.5em;
            color: #0ea5e9;
        }

        .timeline-header h4 {
            font-size: 1.3em;
            font-weight: 800;
            color: #0c4a6e;
            margin: 0;
        }

        .timeline-items-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .timeline-item-summary {
            display: flex;
            align-items: center;
            gap: 15px;
            background: rgba(255, 255, 255, 0.7);
            padding: 15px 20px;
            border-radius: 16px;
            border: 1px solid rgba(14, 165, 233, 0.2);
        }

        .time-badge {
            background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 800;
            font-size: 0.9em;
            min-width: 80px;
            text-align: center;
            box-shadow: 0 3px 10px rgba(14, 165, 233, 0.3);
        }

        .timeline-item-summary span:last-child {
            font-weight: 600;
            color: #0c4a6e;
        }

        /* GOVERNMENT WEBSITE STYLES */

        /* Government Header */
        .government-header {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            padding: 30px 0;
            box-shadow: 0 4px 20px rgba(30, 64, 175, 0.3);
            border-bottom: 4px solid #1d4ed8;
        }

        .container-government {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 30px;
        }

        .gov-header-content {
            display: flex;
            align-items: center;
            gap: 25px;
            margin-bottom: 25px;
        }

        .gov-seal {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.15);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5em;
            color: white;
            border: 3px solid rgba(255, 255, 255, 0.3);
        }

        .gov-title-section {
            flex: 1;
            color: white;
        }

        .gov-main-title {
            font-size: 2.2em;
            font-weight: 800;
            margin: 0 0 10px 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .gov-reference {
            display: flex;
            align-items: center;
            gap: 10px;
            font-size: 0.95em;
        }

        .ref-label {
            font-weight: 700;
            opacity: 0.9;
        }

        .ref-text {
            font-weight: 500;
            opacity: 0.8;
        }

        .gov-notice {
            background: rgba(255, 255, 255, 0.1);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            color: white;
        }

        .notice-header {
            display: flex;
            align-items: center;
            gap: 10px;
            font-weight: 800;
            font-size: 1.1em;
            margin-bottom: 8px;
        }

        .notice-header i {
            color: #fbbf24;
        }

        .gov-notice p {
            margin: 0;
            font-weight: 600;
            line-height: 1.5;
        }

        .client-status {
            display: flex;
            align-items: center;
            gap: 12px;
            background: rgba(255, 255, 255, 0.15);
            padding: 15px 20px;
            border-radius: 25px;
            color: white;
            font-weight: 700;
            border: 2px solid rgba(255, 255, 255, 0.3);
            justify-content: center;
        }

        .status-icon {
            font-size: 1.2em;
        }

        /* Government Content Sections */
        .government-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 30px;
        }

        .gov-section {
            background: #ffffff;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            margin-bottom: 30px;
            overflow: hidden;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
        }

        .section-header {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 25px 30px;
            border-bottom: 2px solid #e5e7eb;
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .section-number {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5em;
            font-weight: 900;
            box-shadow: 0 4px 12px rgba(30, 64, 175, 0.3);
        }

        .section-title h2 {
            font-size: 1.6em;
            font-weight: 800;
            color: #1f2937;
            margin: 0 0 5px 0;
        }

        .section-title p {
            font-size: 1em;
            color: #6b7280;
            margin: 0;
            font-weight: 600;
        }

        .section-content {
            padding: 30px;
        }

        .subsection {
            margin-bottom: 30px;
        }

        .subsection:last-child {
            margin-bottom: 0;
        }

        .subsection h3 {
            font-size: 1.3em;
            font-weight: 800;
            color: #1f2937;
            margin: 0 0 20px 0;
            padding-bottom: 10px;
            border-bottom: 2px solid #e5e7eb;
        }

        /* Legal References Government Style */
        .legal-references-gov {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .ref-item {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            padding: 15px;
            background: #f8fafc;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            border-right: 4px solid #1e40af;
        }

        .ref-code {
            font-weight: 800;
            color: #1e40af;
            font-size: 0.95em;
            min-width: 200px;
        }

        .ref-desc {
            color: #4b5563;
            font-weight: 600;
            font-size: 0.9em;
        }

        /* Service List Government Style */
        .service-list-gov {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .service-item-gov {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            padding: 15px;
            background: #f8fafc;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
        }

        .item-marker {
            width: 30px;
            height: 30px;
            background: #1e40af;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 800;
            font-size: 0.9em;
            flex-shrink: 0;
        }

        .item-text {
            font-size: 0.95em;
            color: #374151;
            line-height: 1.5;
        }

        .item-text strong {
            color: #1f2937;
            font-weight: 800;
        }

        /* Standards Government Style */
        .standards-gov {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .standard-badge {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 15px;
            background: #f0f9ff;
            border: 2px solid #0ea5e9;
            border-radius: 8px;
            color: #0c4a6e;
            font-weight: 700;
            font-size: 0.9em;
        }

        .standard-badge i {
            color: #0ea5e9;
            font-size: 1.2em;
        }

        /* Government Tables and Lists */
        .pricing-table-gov {
            background: #f8fafc;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
        }

        .pricing-main-row {
            display: flex;
            align-items: center;
            gap: 25px;
            margin-bottom: 25px;
            padding-bottom: 25px;
            border-bottom: 2px solid #e5e7eb;
        }

        .pricing-rate {
            font-size: 3em;
            font-weight: 900;
            color: #1e40af;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .pricing-description {
            flex: 1;
        }

        .pricing-title {
            font-size: 1.3em;
            font-weight: 800;
            color: #1f2937;
            margin-bottom: 5px;
        }

        .pricing-subtitle {
            font-size: 1em;
            color: #6b7280;
            font-weight: 600;
        }

        .pricing-conditions-gov {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 12px;
        }

        .condition-positive,
        .condition-neutral {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 12px 15px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 0.9em;
        }

        .condition-positive {
            background: #dcfce7;
            border: 1px solid #16a34a;
            color: #15803d;
        }

        .condition-positive i {
            color: #16a34a;
        }

        .condition-neutral {
            background: #fef3c7;
            border: 1px solid #d97706;
            color: #92400e;
        }

        .condition-neutral i {
            color: #d97706;
        }

        .calculation-table-gov {
            background: #ffffff;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
            margin: 20px 0;
        }

        .calc-header {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            background: #1e40af;
            color: white;
            font-weight: 800;
            font-size: 0.95em;
        }

        .calc-header span {
            padding: 15px;
            text-align: center;
            border-left: 1px solid rgba(255,255,255,0.2);
        }

        .calc-header span:first-child {
            border-left: none;
        }

        .calc-row {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            border-bottom: 1px solid #e5e7eb;
        }

        .calc-row:last-child {
            border-bottom: none;
        }

        .calc-row span {
            padding: 15px;
            text-align: center;
            font-weight: 600;
            border-left: 1px solid #e5e7eb;
        }

        .calc-row span:first-child {
            border-left: none;
        }

        .amount {
            color: #1f2937;
            font-weight: 800;
        }

        .fee {
            color: #059669;
            font-weight: 800;
        }

        .status.success {
            color: #059669;
            font-weight: 800;
        }

        .status.neutral {
            color: #d97706;
            font-weight: 800;
        }

        .timeline-gov {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .timeline-item-gov {
            display: flex;
            align-items: center;
            gap: 20px;
            padding: 15px;
            background: #f8fafc;
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            border-right: 4px solid #0ea5e9;
        }

        .timeline-period {
            background: #0ea5e9;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 800;
            font-size: 0.9em;
            min-width: 100px;
            text-align: center;
        }

        .timeline-desc {
            color: #374151;
            font-weight: 600;
            font-size: 0.95em;
        }

        /* Power of Attorney Government Styles */
        .authority-scope-gov {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 25px;
            margin: 20px 0;
        }

        .scope-section {
            background: #ffffff;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            overflow: hidden;
        }

        .scope-section.authorized {
            border-color: #16a34a;
        }

        .scope-section.limited {
            border-color: #0ea5e9;
        }

        .scope-header {
            padding: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
            font-weight: 800;
        }

        .scope-section.authorized .scope-header {
            background: #dcfce7;
            color: #15803d;
        }

        .scope-section.limited .scope-header {
            background: #dbeafe;
            color: #1e40af;
        }

        .scope-header i {
            font-size: 1.5em;
        }

        .scope-header h4 {
            margin: 0;
            font-size: 1.2em;
        }

        .scope-list {
            padding: 20px;
        }

        .scope-item-gov {
            padding: 10px 15px;
            margin-bottom: 8px;
            background: #f8fafc;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            font-weight: 600;
            font-size: 0.9em;
            color: #374151;
        }

        .scope-item-gov:last-child {
            margin-bottom: 0;
        }

        .validity-table-gov {
            background: #f8fafc;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
            margin: 20px 0;
        }

        .validity-row {
            display: grid;
            grid-template-columns: 200px 1fr;
            border-bottom: 1px solid #e5e7eb;
        }

        .validity-row:last-child {
            border-bottom: none;
        }

        .validity-label {
            padding: 15px 20px;
            background: #f1f5f9;
            font-weight: 800;
            color: #1f2937;
            border-left: 4px solid #1e40af;
        }

        .validity-value {
            padding: 15px 20px;
            color: #374151;
            font-weight: 600;
        }

        .form-access-gov {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px;
            background: #f0f9ff;
            border: 2px solid #0ea5e9;
            border-radius: 12px;
            margin: 20px 0;
        }

        .form-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .form-info i {
            font-size: 2em;
            color: #0ea5e9;
        }

        .form-details {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .form-title {
            font-weight: 800;
            color: #0c4a6e;
            font-size: 1.1em;
        }

        .form-desc {
            color: #0369a1;
            font-weight: 600;
            font-size: 0.9em;
        }

        .view-form-btn {
            display: flex;
            align-items: center;
            gap: 8px;
            background: #0ea5e9;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .view-form-btn:hover {
            background: #0284c7;
            transform: translateY(-2px);
        }

        /* Privacy and Compliance Government Styles */
        .security-measures-gov {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .security-item {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 20px;
            background: #f8fafc;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            transition: all 0.3s ease;
        }

        .security-item:hover {
            border-color: #cbd5e1;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .security-icon {
            width: 50px;
            height: 50px;
            background: #1e40af;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.3em;
            flex-shrink: 0;
        }

        .security-details {
            flex: 1;
        }

        .security-title {
            font-weight: 800;
            color: #1f2937;
            font-size: 1em;
            margin-bottom: 5px;
        }

        .security-desc {
            color: #6b7280;
            font-weight: 600;
            font-size: 0.9em;
        }

        .compliance-table-gov {
            background: #ffffff;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            overflow: hidden;
            margin: 20px 0;
        }

        .compliance-row {
            display: grid;
            grid-template-columns: 200px 1fr 120px;
            border-bottom: 1px solid #e5e7eb;
        }

        .compliance-row:last-child {
            border-bottom: none;
        }

        .compliance-type {
            padding: 15px 20px;
            background: #f1f5f9;
            font-weight: 800;
            color: #1f2937;
            border-left: 4px solid #1e40af;
        }

        .compliance-details {
            padding: 15px 20px;
            color: #374151;
            font-weight: 600;
            border-left: 1px solid #e5e7eb;
        }

        .compliance-status {
            padding: 15px 20px;
            text-align: center;
            font-weight: 800;
            border-left: 1px solid #e5e7eb;
        }

        .compliance-status.active {
            color: #059669;
            background: #dcfce7;
        }

        .rights-gov {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .right-item-gov {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 15px;
            background: #f0f9ff;
            border: 2px solid #0ea5e9;
            border-radius: 8px;
            color: #0c4a6e;
            font-weight: 700;
            font-size: 0.9em;
        }

        .right-item-gov i {
            color: #0ea5e9;
            font-size: 1.1em;
        }

        /* Official Disclaimer Styles */
        .official-disclaimer-section {
            background: #fef3c7;
            border: 3px solid #f59e0b;
            border-radius: 12px;
            margin: 25px 0;
            padding: 25px;
            font-family: 'Times New Roman', serif;
        }

        .disclaimer-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f59e0b;
        }

        .disclaimer-icon {
            font-size: 2em;
            color: #92400e;
        }

        .disclaimer-header h4 {
            font-size: 1.4em;
            font-weight: 800;
            color: #92400e;
            margin: 0;
        }

        .disclaimer-content {
            line-height: 1.8;
            color: #92400e;
            font-size: 0.95em;
        }

        .disclaimer-content p {
            margin: 15px 0;
            text-align: justify;
            font-weight: 600;
        }

        .disclaimer-content p:first-child {
            margin-top: 0;
        }

        .disclaimer-content p:last-child {
            margin-bottom: 0;
        }

        .disclaimer-content strong {
            font-weight: 800;
            color: #78350f;
        }

        /* Beautiful Proceed Button */
        .action-buttons-clean {
            display: flex;
            justify-content: center;
            margin: 50px 0;
        }

        .proceed-btn-clean {
            display: flex;
            align-items: center;
            gap: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #667eea 100%);
            color: white;
            border: none;
            padding: 25px 40px;
            border-radius: 20px;
            font-weight: 800;
            cursor: pointer;
            transition: all 0.4s ease;
            box-shadow: 0 12px 40px rgba(102, 126, 234, 0.4);
            min-width: 400px;
            text-align: left;
            position: relative;
            overflow: hidden;
        }

        .proceed-btn-clean::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, transparent 0%, rgba(255, 255, 255, 0.1) 50%, transparent 100%);
            pointer-events: none;
        }

        .proceed-btn-clean:disabled {
            background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
            cursor: not-allowed;
            transform: none;
            box-shadow: 0 6px 20px rgba(156, 163, 175, 0.3);
        }

        .proceed-btn-clean:not(:disabled):hover {
            transform: translateY(-5px) scale(1.02);
            box-shadow: 0 20px 60px rgba(102, 126, 234, 0.5);
        }

        .proceed-btn-clean:not(:disabled):active {
            transform: translateY(-2px) scale(1.01);
        }

        .btn-icon-clean {
            width: 50px;
            height: 50px;
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.15) 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.4em;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 2;
        }

        .btn-content-clean {
            flex: 1;
        }

        .btn-title-clean {
            font-size: 1.3em;
            font-weight: 900;
            margin-bottom: 6px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
            position: relative;
            z-index: 2;
        }

        .btn-subtitle-clean {
            font-size: 1em;
            opacity: 0.95;
            font-weight: 600;
            position: relative;
            z-index: 2;
        }

        /* Clean Responsive */
        @media (max-width: 768px) {
            .header-content-clean {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .header-info-clean {
                flex-direction: column;
                gap: 10px;
            }

            .progress-steps-clean {
                flex-direction: column;
                gap: 15px;
                align-items: center;
            }

            .step-clean {
                min-width: 250px;
            }

            .main-title-clean {
                font-size: 1.8em;
            }

            .main-subtitle-clean {
                font-size: 1em;
            }

            .proceed-btn-clean {
                min-width: 280px;
                padding: 18px 25px;
            }

            .help-button-clean {
                bottom: 20px;
                left: 20px;
            }
        }

        /* UNIFIED AGREEMENT STYLES */
        .unified-agreement-section {
            margin: 30px 0;
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            border-radius: 24px;
            box-shadow: 0 12px 48px rgba(0, 0, 0, 0.08);
            border: 2px solid rgba(226, 232, 240, 0.6);
            overflow: hidden;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .unified-agreement-section:hover {
            box-shadow: 0 16px 56px rgba(0, 0, 0, 0.12);
            transform: translateY(-2px);
        }

        .agreement-header-unified {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            padding: 25px 30px;
            display: flex;
            align-items: center;
            gap: 20px;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .agreement-header-unified::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%, rgba(255,255,255,0.1) 100%);
            background-size: 200% 200%;
            animation: agreementShine 4s ease-in-out infinite;
        }

        @keyframes agreementShine {
            0%, 100% { background-position: 200% 200%; }
            50% { background-position: -200% -200%; }
        }

        .agreement-icon-unified {
            width: 60px;
            height: 60px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.3);
            position: relative;
            z-index: 2;
        }

        .agreement-icon-unified i {
            font-size: 1.8em;
            color: white;
            animation: agreementIconPulse 2s ease-in-out infinite;
        }

        @keyframes agreementIconPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .agreement-title-unified {
            position: relative;
            z-index: 2;
        }

        .agreement-title-unified h4 {
            font-size: 1.4em;
            font-weight: 800;
            margin: 0 0 5px 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        .agreement-title-unified p {
            font-size: 0.95em;
            margin: 0;
            opacity: 0.9;
            font-weight: 500;
        }

        .unified-checkbox-wrapper {
            padding: 35px;
        }

        .unified-checkbox-label {
            display: flex;
            align-items: flex-start;
            gap: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .unified-checkbox-label:hover {
            transform: translateY(-1px);
        }

        .unified-checkbox-input {
            display: none;
        }

        .unified-checkmark-container {
            flex-shrink: 0;
            margin-top: 5px;
        }

        .unified-checkmark-circle {
            width: 50px;
            height: 50px;
            border: 3px solid #e2e8f0;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
        }

        .unified-checkbox-input:checked + .unified-checkmark-container .unified-checkmark-circle {
            border-color: #10b981;
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            transform: scale(1.1);
            box-shadow: 0 8px 24px rgba(16, 185, 129, 0.3);
        }

        .unified-checkmark-icon {
            font-size: 1.5em;
            color: white;
            opacity: 0;
            transform: scale(0);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .unified-checkbox-input:checked + .unified-checkmark-container .unified-checkmark-icon {
            opacity: 1;
            transform: scale(1);
        }

        .unified-agreement-content {
            flex: 1;
        }

        .agreement-main-title {
            font-size: 1.3em;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 20px;
            text-shadow: 0 1px 2px rgba(0,0,0,0.05);
        }

        .agreement-items-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
            margin-bottom: 20px;
        }

        .agreement-item-unified {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            background: rgba(16, 185, 129, 0.05);
            border-radius: 12px;
            border: 1px solid rgba(16, 185, 129, 0.1);
            transition: all 0.3s ease;
        }

        .agreement-item-unified:hover {
            background: rgba(16, 185, 129, 0.08);
            border-color: rgba(16, 185, 129, 0.2);
            transform: translateX(5px);
        }

        .item-icon {
            color: #10b981;
            font-size: 1.1em;
            flex-shrink: 0;
        }

        .agreement-item-unified span {
            color: #374151;
            font-weight: 600;
            font-size: 0.95em;
            line-height: 1.4;
        }

        .agreement-legal-note {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 15px 20px;
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border-radius: 12px;
            border: 2px solid #f59e0b;
            color: #92400e;
            font-weight: 600;
            font-size: 0.9em;
        }

        .agreement-legal-note i {
            color: #f59e0b;
            font-size: 1.2em;
        }

        /* Professional Responsive Design */
        @media (max-width: 768px) {
            .professional-header {
                padding: 20px 0;
            }

            .container-professional {
                padding: 0 20px;
            }

            .header-content-professional {
                flex-direction: column;
                gap: 20px;
                text-align: center;
            }

            .credentials-section {
                flex-direction: column;
                gap: 15px;
                width: 100%;
            }

            .credential-item {
                padding: 12px 15px;
            }

            .logo-icon-professional {
                width: 50px;
                height: 50px;
                font-size: 1.5em;
            }

            .logo-text-professional h1 {
                font-size: 1.8em;
            }

            .legal-header {
                flex-direction: column;
                gap: 20px;
            }

            .legal-seal {
                width: 60px;
                height: 60px;
                font-size: 2em;
            }

            .main-title-professional {
                font-size: 2em;
            }

            .legal-notice-box {
                flex-direction: column;
                text-align: center;
                gap: 15px;
            }

            .terms-header-professional {
                flex-direction: column;
                gap: 15px;
                text-align: center;
                padding: 15px 20px;
            }

            .terms-content-professional {
                padding: 20px;
            }

            .legal-ref {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }

            .service-item {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }

            .standards-grid {
                grid-template-columns: 1fr;
            }

            .pricing-main-professional {
                flex-direction: column;
                text-align: center;
                gap: 15px;
            }

            .pricing-percentage {
                font-size: 2.5em;
            }

            .pricing-conditions {
                grid-template-columns: 1fr;
            }

            .example-row {
                grid-template-columns: 1fr;
                gap: 10px;
                text-align: right;
            }

            .example-row span:before {
                content: attr(data-label) ": ";
                font-weight: 700;
                color: #1e3a8a;
            }

            .timeline-item {
                flex-direction: column;
                text-align: center;
                gap: 10px;
            }

            .signature-workspace-clean {
                padding: 20px;
            }

            .signature-instructions-clean {
                padding: 15px;
                margin-bottom: 20px;
            }

            .instruction-methods {
                flex-direction: column;
                gap: 15px;
            }

            .method-item {
                justify-content: center;
                font-size: 0.9em;
            }

            .signature-area-clean {
                padding: 20px;
                min-height: 150px;
            }

            #digitalSignatureCanvas {
                width: 100%;
                max-width: 350px;
                height: 110px;
            }

            .prompt-icon-clean {
                font-size: 2em;
            }

            .prompt-main-clean {
                font-size: 1.1em;
            }

            .prompt-sub-clean {
                font-size: 0.85em;
            }

            .signature-controls-clean {
                flex-direction: column;
                gap: 15px;
                padding: 15px 20px;
                margin-top: 20px;
            }

            .control-left-clean,
            .control-right-clean {
                width: 100%;
                justify-content: center;
            }

            .proceed-btn-clean {
                min-width: 280px;
                padding: 18px 25px;
            }

            .protection-grid {
                grid-template-columns: 1fr;
            }

            .compliance-item {
                flex-direction: column;
                text-align: center;
                gap: 10px;
            }

            .rights-list {
                grid-template-columns: 1fr;
            }

            .summary-header {
                flex-direction: column;
                text-align: center;
                gap: 15px;
            }

            .summary-icon {
                width: 50px;
                height: 50px;
                font-size: 1.5em;
            }

            .legal-declaration,
            .summary-header {
                padding: 20px;
            }

            .scope-item {
                flex-direction: column;
                text-align: center;
                gap: 10px;
            }

            .validity-item {
                flex-direction: column;
                text-align: center;
                gap: 10px;
            }

            .service-cards-grid {
                grid-template-columns: 1fr;
            }

            .pricing-badge {
                flex-direction: column;
                gap: 20px;
            }

            .percentage-large {
                font-size: 3em;
            }

            .pricing-examples-cards {
                grid-template-columns: 1fr;
            }

            .timeline-items-summary {
                grid-template-columns: 1fr;
            }

            .pricing-benefits-grid {
                grid-template-columns: 1fr;
            }

            .legal-basis-summary {
                flex-direction: column;
                text-align: center;
                gap: 20px;
            }

            .timeline-summary {
                padding: 20px;
            }

            .pricing-main-card {
                padding: 30px 20px;
            }

            /* Government Responsive */
            .gov-header-content {
                flex-direction: column;
                text-align: center;
                gap: 20px;
            }

            .gov-reference {
                flex-direction: column;
                gap: 5px;
                text-align: center;
            }

            .government-content {
                padding: 20px 15px;
            }

            .section-header {
                flex-direction: column;
                text-align: center;
                gap: 15px;
                padding: 20px;
            }

            .section-content {
                padding: 20px;
            }

            .legal-references-gov {
                gap: 10px;
            }

            .ref-item {
                flex-direction: column;
                gap: 10px;
                text-align: center;
            }

            .ref-code {
                min-width: auto;
            }

            .service-list-gov {
                gap: 12px;
            }

            .service-item-gov {
                flex-direction: column;
                text-align: center;
                gap: 10px;
            }

            .standards-gov {
                grid-template-columns: 1fr;
            }

            .pricing-main-row {
                flex-direction: column;
                text-align: center;
                gap: 15px;
            }

            .pricing-rate {
                font-size: 2.5em;
            }

            .pricing-conditions-gov {
                grid-template-columns: 1fr;
            }

            .calc-header,
            .calc-row {
                grid-template-columns: 1fr;
                text-align: center;
            }

            .calc-header span,
            .calc-row span {
                border-left: none;
                border-bottom: 1px solid rgba(255,255,255,0.2);
            }

            .calc-row span {
                border-bottom: 1px solid #e5e7eb;
            }

            .timeline-item-gov {
                flex-direction: column;
                text-align: center;
                gap: 10px;
            }

            .authority-scope-gov {
                grid-template-columns: 1fr;
            }

            .validity-row {
                grid-template-columns: 1fr;
                text-align: center;
            }

            .validity-label {
                border-left: none;
                border-bottom: 1px solid #e5e7eb;
            }

            .form-access-gov {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .security-measures-gov {
                grid-template-columns: 1fr;
            }

            .compliance-row {
                grid-template-columns: 1fr;
                text-align: center;
            }

            .compliance-type,
            .compliance-details {
                border-left: none;
                border-bottom: 1px solid #e5e7eb;
            }

            .rights-gov {
                grid-template-columns: 1fr;
            }

            /* Official Disclaimer Responsive */
            .official-disclaimer-section {
                padding: 20px;
                margin: 20px 0;
            }

            .disclaimer-header {
                flex-direction: column;
                text-align: center;
                gap: 10px;
            }

            .disclaimer-content {
                font-size: 0.9em;
            }

            .disclaimer-content p {
                margin: 12px 0;
            }
        }

        @media (max-width: 480px) {
            .beautiful-signature-container {
                margin: 15px 0;
                border-radius: 12px;
            }

            .signature-header {
                padding: 15px 18px;
            }

            .signature-title {
                font-size: 1em;
            }

            .signature-icon {
                font-size: 1.2em;
            }

            .date-label {
                font-size: 0.85em;
            }

            .date-value {
                font-size: 0.9em;
                min-width: 75px;
            }

            .signature-pad-container {
                padding: 15px;
            }

            .signature-background {
                padding: 12px;
                min-height: 120px;
            }

            #digitalSignatureCanvas {
                max-width: 280px;
                height: 80px;
            }

            .prompt-icon {
                font-size: 1.8em;
            }

            .prompt-main {
                font-size: 1em;
            }

            .prompt-sub {
                font-size: 0.8em;
            }

            .signature-controls-beautiful {
                padding: 15px 18px;
                margin-top: 15px;
                gap: 12px;
            }

            .clear-btn-beautiful {
                padding: 10px 16px;
                font-size: 0.9em;
            }

            .signature-status-beautiful {
                padding: 8px 14px;
            }

            .status-text {
                font-size: 0.9em;
            }
        }

        /* Ultra Small Screens */
        @media (max-width: 360px) {
            .signature-pad-container {
                padding: 12px;
            }

            .signature-background {
                padding: 10px;
                min-height: 100px;
            }

            #digitalSignatureCanvas {
                max-width: 260px;
                height: 70px;
            }

            .signature-controls-beautiful {
                padding: 12px 15px;
            }

            .clear-btn-beautiful {
                padding: 8px 14px;
                font-size: 0.85em;
            }
        }



        .signature-area {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin-top: 20px;
        }

        .signature-field {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .signature-field label {
            font-weight: 700;
            color: #2c3e50;
            min-width: 80px;
        }

        /* Comprehensive Agreement Styles */
        .comprehensive-agreement-section {
            background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
            border: 3px solid #0288d1;
            border-radius: 25px;
            padding: 40px;
            margin: 40px 0;
            position: relative;
        }

        .comprehensive-agreement-section::before {
            content: '📋';
            position: absolute;
            top: -25px;
            right: 40px;
            background: #0288d1;
            color: white;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2em;
            box-shadow: 0 8px 20px rgba(2, 136, 209, 0.3);
        }

        .comprehensive-agreement-section h4 {
            color: #01579b;
            font-size: 2em;
            font-weight: 800;
            margin-bottom: 25px;
            text-align: center;
        }

        .comprehensive-agreement-content {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            padding: 30px;
            margin: 25px 0;
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.05);
        }

        .agreement-section-detailed {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border-right: 5px solid #0288d1;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }

        .agreement-section-detailed:hover {
            transform: translateX(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.12);
            border-right-color: #10b981;
        }

        .agreement-section-detailed h5 {
            color: #01579b;
            font-size: 1.3em;
            font-weight: 700;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .agreement-section-detailed ul {
            margin: 15px 0;
            padding-right: 25px;
        }

        .agreement-section-detailed li {
            margin: 12px 0;
            line-height: 1.6;
            color: #2c3e50;
            font-weight: 500;
            position: relative;
        }

        .agreement-section-detailed li::before {
            content: '✓';
            position: absolute;
            right: -25px;
            top: 0;
            color: #10b981;
            font-weight: bold;
            font-size: 1.1em;
        }

        .final-agreement-checkbox {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 3px solid #f59e0b;
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            position: relative;
        }

        .final-agreement-checkbox::before {
            content: '⚖️';
            position: absolute;
            top: -20px;
            right: 30px;
            background: #f59e0b;
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5em;
            box-shadow: 0 5px 15px rgba(245, 158, 11, 0.3);
        }

        .comprehensive-agreement-item {
            display: flex;
            align-items: flex-start;
            gap: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .comprehensive-agreement-item:hover {
            transform: translateX(5px);
        }

        .comprehensive-agreement-item input[type="checkbox"] {
            display: none;
        }

        .comprehensive-checkmark {
            width: 35px;
            height: 35px;
            border: 4px solid #f59e0b;
            border-radius: 8px;
            position: relative;
            transition: all 0.3s ease;
            flex-shrink: 0;
            margin-top: 5px;
            background: white;
        }

        .comprehensive-agreement-item input[type="checkbox"]:checked + .comprehensive-checkmark {
            background: linear-gradient(135deg, #10b981, #059669);
            border-color: #10b981;
            transform: scale(1.1);
        }

        .comprehensive-agreement-item input[type="checkbox"]:checked + .comprehensive-checkmark::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-weight: bold;
            font-size: 20px;
        }

        .agreement-text {
            color: #92400e;
            font-size: 1.1em;
            line-height: 1.7;
            font-weight: 600;
        }

        /* Theme Toggle Styles */
        .theme-toggle {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: var(--bg-primary);
            border: 2px solid var(--border-color);
            border-radius: 50px;
            padding: 12px 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px var(--shadow-color);
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .theme-toggle:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px var(--shadow-color);
        }

        .theme-icon {
            font-size: 1.2em;
            transition: transform 0.3s ease;
        }

        .theme-toggle:hover .theme-icon {
            transform: rotate(180deg);
        }

        /* Enhanced Accessibility Controls */
        .accessibility-controls {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: var(--z-fixed);
            display: flex;
            flex-direction: column;
            gap: var(--space-2);
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: var(--space-1);
        }

        .control-btn {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: var(--radius-full);
            padding: var(--space-3) var(--space-4);
            cursor: pointer;
            transition: all var(--transition-normal);
            display: flex;
            align-items: center;
            gap: var(--space-2);
            font-weight: 600;
            color: var(--text-primary);
            box-shadow: var(--shadow-md);
            font-size: var(--text-sm);
            min-width: 120px;
            justify-content: flex-start;
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .control-btn:focus {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
        }

        .help-btn {
            background: linear-gradient(135deg, var(--info-color), var(--info-dark));
            color: white;
            border-color: var(--info-color);
        }

        .help-btn:hover {
            background: linear-gradient(135deg, var(--info-dark), var(--info-color));
        }

        /* Enhanced Theme States */
        [data-theme="dark"] .control-btn {
            background: rgba(31, 41, 55, 0.95);
            color: white;
            border-color: rgba(255, 255, 255, 0.2);
        }

        [data-font-size="large"] {
            font-size: 1.125rem;
        }

        [data-reading-mode="true"] {
            background: #f5f5dc !important;
            color: #2c3e50 !important;
        }

        /* Icon animations */
        .font-icon,
        .contrast-icon,
        .reading-icon,
        .help-icon {
            font-size: 1.2em;
            transition: transform var(--transition-normal);
        }

        .control-btn:hover .font-icon {
            transform: scale(1.2);
        }

        .control-btn:hover .contrast-icon {
            transform: rotateY(180deg);
        }

        .control-btn:hover .reading-icon {
            transform: rotateX(180deg);
        }

        .control-btn:hover .help-icon {
            transform: rotate(360deg);
        }

        /* Auto-save indicator */
        .auto-save-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: var(--success-color);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 600;
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.3s ease;
        }

        .auto-save-indicator.show {
            opacity: 1;
            transform: translateY(0);
        }

        /* Help Modal Styles */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: var(--z-modal);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: var(--bg-primary);
            border-radius: var(--radius-2xl);
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow: hidden;
            box-shadow: var(--shadow-2xl);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: scale(0.9) translateY(-20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--space-6);
            border-bottom: 1px solid var(--border-color);
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
        }

        .modal-header h2 {
            margin: 0;
            display: flex;
            align-items: center;
            gap: var(--space-3);
            font-size: var(--text-2xl);
        }

        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: var(--text-xl);
            cursor: pointer;
            padding: var(--space-2);
            border-radius: var(--radius-md);
            transition: background var(--transition-fast);
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .modal-body {
            padding: var(--space-6);
            max-height: 60vh;
            overflow-y: auto;
        }

        .help-tabs {
            display: flex;
            gap: var(--space-2);
            margin-bottom: var(--space-6);
            border-bottom: 1px solid var(--border-color);
            flex-wrap: wrap;
        }

        .tab-btn {
            background: none;
            border: none;
            padding: var(--space-3) var(--space-4);
            cursor: pointer;
            border-radius: var(--radius-lg) var(--radius-lg) 0 0;
            transition: all var(--transition-fast);
            display: flex;
            align-items: center;
            gap: var(--space-2);
            font-weight: 600;
            color: var(--text-secondary);
        }

        .tab-btn.active {
            background: var(--primary-color);
            color: white;
        }

        .tab-btn:hover:not(.active) {
            background: var(--gray-100);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .help-steps {
            display: flex;
            flex-direction: column;
            gap: var(--space-4);
        }

        .help-step {
            display: flex;
            gap: var(--space-4);
            padding: var(--space-4);
            background: var(--gray-50);
            border-radius: var(--radius-lg);
            border-left: 4px solid var(--primary-color);
        }

        .step-number {
            width: 40px;
            height: 40px;
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            flex-shrink: 0;
        }

        .step-content h4 {
            margin: 0 0 var(--space-2) 0;
            color: var(--text-primary);
        }

        .step-content p {
            margin: 0;
            color: var(--text-secondary);
            line-height: 1.6;
        }

        .faq-list {
            display: flex;
            flex-direction: column;
            gap: var(--space-3);
        }

        .faq-item {
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            overflow: hidden;
        }

        .faq-question {
            width: 100%;
            background: none;
            border: none;
            padding: var(--space-4);
            text-align: right;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: 600;
            transition: background var(--transition-fast);
        }

        .faq-question:hover {
            background: var(--gray-50);
        }

        .faq-answer {
            padding: 0 var(--space-4) var(--space-4) var(--space-4);
            display: none;
            color: var(--text-secondary);
            line-height: 1.6;
        }

        .contact-info,
        .accessibility-features {
            display: flex;
            flex-direction: column;
            gap: var(--space-4);
        }

        .contact-item,
        .feature-item {
            display: flex;
            gap: var(--space-4);
            padding: var(--space-4);
            background: var(--gray-50);
            border-radius: var(--radius-lg);
            align-items: flex-start;
        }

        .contact-item i,
        .feature-item i {
            color: var(--primary-color);
            font-size: var(--text-xl);
            margin-top: var(--space-1);
        }

        .modal-footer {
            display: flex;
            justify-content: space-between;
            padding: var(--space-6);
            border-top: 1px solid var(--border-color);
            background: var(--gray-50);
        }

        .btn-secondary,
        .btn-primary {
            padding: var(--space-3) var(--space-6);
            border: none;
            border-radius: var(--radius-lg);
            font-weight: 600;
            cursor: pointer;
            transition: all var(--transition-fast);
            display: flex;
            align-items: center;
            gap: var(--space-2);
        }

        .btn-secondary {
            background: var(--gray-200);
            color: var(--text-primary);
        }

        .btn-secondary:hover {
            background: var(--gray-300);
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-dark);
        }

        /* Responsive Modal */
        @media (max-width: 768px) {
            .modal-content {
                width: 95%;
                max-height: 95vh;
            }

            .modal-header,
            .modal-body,
            .modal-footer {
                padding: var(--space-4);
            }

            .help-tabs {
                flex-direction: column;
            }

            .tab-btn {
                justify-content: center;
            }

            .help-step {
                flex-direction: column;
                text-align: center;
            }

            .modal-footer {
                flex-direction: column;
                gap: var(--space-3);
            }

            .btn-secondary,
            .btn-primary {
                justify-content: center;
            }
        }

        /* Recovery dialog */
        .recovery-dialog {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 2000;
            display: none;
            align-items: center;
            justify-content: center;
        }

        .recovery-content {
            background: var(--bg-primary);
            border-radius: 20px;
            padding: 30px;
            max-width: 500px;
            margin: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .recovery-content h3 {
            color: var(--text-primary);
            margin-bottom: 15px;
            font-size: 1.5em;
        }

        .recovery-content p {
            color: var(--text-secondary);
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .recovery-buttons {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
        }

        .recovery-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .recovery-btn.primary {
            background: var(--primary-color);
            color: white;
        }

        .recovery-btn.secondary {
            background: var(--bg-secondary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }



        .section-title {
            text-align: center;
            font-size: 3em;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 800;
            letter-spacing: -1px;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .section-subtitle {
            text-align: center;
            font-size: 1.3em;
            color: #555;
            margin-bottom: 50px;
            font-weight: 400;
            line-height: 1.6;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        /* OCR Styles */
        .ocr-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 40px;
            border-radius: 20px;
            margin-bottom: 40px;
            border: 2px solid transparent;
            background-clip: padding-box;
            position: relative;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .ocr-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 20px;
            padding: 2px;
            background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
            z-index: -1;
        }

        .ocr-section h3 {
            color: #2c3e50;
            font-size: 1.8em;
            font-weight: 700;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .ocr-section p {
            color: #555;
            font-size: 1.1em;
            margin-bottom: 25px;
            line-height: 1.6;
        }

        .upload-area {
            border: 3px dashed #d1d5db;
            border-radius: 20px;
            padding: 50px;
            text-align: center;
            background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            margin: 25px 0;
            position: relative;
            overflow: hidden;
        }

        .upload-area::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
            transition: left 0.5s;
        }

        .upload-area:hover::before {
            left: 100%;
        }

        .upload-area:hover,
        .upload-area.dragover {
            border-color: #667eea;
            background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
            transform: translateY(-2px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.2);
        }

        .upload-content {
            pointer-events: none;
        }

        .upload-icon {
            font-size: 5em;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: uploadPulse 2s ease-in-out infinite;
        }

        @keyframes uploadPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .upload-content h4 {
            font-size: 1.5em;
            color: #2c3e50;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .upload-content p {
            color: #6b7280;
            font-size: 1.1em;
            margin-bottom: 25px;
        }

        .upload-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            color: white;
            padding: 15px 35px;
            border: none;
            border-radius: 30px;
            font-size: 1.2em;
            font-weight: 600;
            cursor: pointer;
            margin-top: 15px;
            pointer-events: all;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        }

        .document-types {
            margin: 25px 0;
        }

        .doc-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }

        .doc-item {
            background: white;
            padding: 10px;
            border-radius: 8px;
            font-size: 0.9em;
            border: 1px solid #e0e0e0;
        }

        .uploaded-files {
            margin: 20px 0;
        }

        .file-item {
            background: white;
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            border: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .file-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .file-status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .status-processing {
            background: #fff3cd;
            color: #856404;
        }

        .status-completed {
            background: #d4edda;
            color: #155724;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
        }

        .ocr-progress {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s;
        }

        .ocr-results {
            background: white;
            padding: 25px;
            border-radius: 10px;
            margin: 20px 0;
            border: 2px solid #27ae60;
        }

        .extracted-data-item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #e0e0e0;
        }

        .apply-data-btn {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            margin-top: 20px;
            width: 100%;
        }

        .divider {
            text-align: center;
            margin: 50px 0;
            position: relative;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, #667eea, transparent);
        }

        .divider span {
            background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
            padding: 15px 30px;
            color: #2c3e50;
            font-weight: 700;
            font-size: 1.1em;
            border-radius: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border: 2px solid #e5e7eb;
        }

        /* Fallback and Modal Styles */
        .fallback-options {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .fallback-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }

        .fallback-btn {
            background: linear-gradient(135deg, #ffc107, #ff8c00);
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .fallback-btn:hover {
            transform: translateY(-2px);
        }

        .tips {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
        }

        .tips ul {
            margin: 10px 0;
            padding-right: 20px;
        }

        .tips li {
            margin: 5px 0;
        }

        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            padding: 30px;
            border-radius: 15px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .interactive-form {
            margin: 20px 0;
        }

        .interactive-form .form-group {
            margin: 15px 0;
        }

        .interactive-form label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }

        .interactive-form input,
        .interactive-form select {
            width: 100%;
            padding: 10px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
        }

        .modal-buttons {
            display: flex;
            gap: 15px;
            margin-top: 25px;
        }

        .submit-btn {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 1.1em;
            cursor: pointer;
            flex: 1;
        }

        .cancel-btn {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 1.1em;
            cursor: pointer;
            flex: 1;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(255, 107, 107, 0); }
            100% { box-shadow: 0 0 0 0 rgba(255, 107, 107, 0); }
        }

        /* Checkbox containers for exemptions */
        .checkbox-container {
            display: flex;
            align-items: center;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 12px;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            background: #f9fafb;
        }

        .checkbox-container:hover {
            background: #f3f4f6;
            border-color: #7c3aed;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(124, 58, 237, 0.1);
        }

        .checkbox-text {
            margin-right: 10px;
            font-weight: 500;
            color: #374151;
            font-size: 0.95em;
        }

        .checkbox-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .info-box {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }

        .info-box h4 {
            color: #0369a1;
            margin: 0 0 10px 0;
            font-size: 1.1em;
        }

        .info-box ul {
            margin: 0;
            padding-right: 20px;
            color: #374151;
        }

        .info-box li {
            margin-bottom: 5px;
        }

        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5em;
            }

            .nav-menu {
                display: none;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .doc-grid {
                grid-template-columns: 1fr;
            }

            .checkbox-grid {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .checkbox-container {
                padding: 10px;
            }
        }

        /* Professional Header Styles */
        .professional-header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo-icon {
            width: 60px;
            height: 60px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        .logo-text h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 700;
            background: linear-gradient(45deg, #fff, #e8f4fd);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .logo-text p {
            margin: 0;
            font-size: 14px;
            opacity: 0.9;
        }

        .header-badges {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .badge {
            display: flex;
            align-items: center;
            gap: 8px;
            background: rgba(255,255,255,0.15);
            padding: 8px 15px;
            border-radius: 25px;
            font-size: 13px;
            font-weight: 500;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .badge.secure { background: rgba(46, 204, 113, 0.2); }
        .badge.certified { background: rgba(241, 196, 15, 0.2); }
        .badge.support { background: rgba(155, 89, 182, 0.2); }

        /* Progress Steps */
        .progress-container {
            background: rgba(255,255,255,0.95);
            padding: 20px 0;
            border-bottom: 1px solid rgba(0,0,0,0.1);
        }

        .progress-steps {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 40px;
            flex-wrap: wrap;
        }

        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            position: relative;
            opacity: 0.5;
            transition: all 0.3s ease;
        }

        .step.completed,
        .step.active {
            opacity: 1;
        }

        .step-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            background: #e9ecef;
            color: #6c757d;
            transition: all 0.3s ease;
        }

        .step.completed .step-icon {
            background: #28a745;
            color: white;
        }

        .step.active .step-icon {
            background: #007bff;
            color: white;
            box-shadow: 0 0 20px rgba(0,123,255,0.3);
        }

        .step-text {
            font-size: 12px;
            font-weight: 600;
            text-align: center;
            color: #495057;
        }

        .step.completed .step-text {
            color: #28a745;
        }

        .step.active .step-text {
            color: #007bff;
        }

        /* Main Content Styles */
        .main-content {
            padding: 40px 0;
            min-height: calc(100vh - 200px);
        }

        .welcome-section {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255,255,255,0.3);
        }

        .welcome-content {
            text-align: center;
        }

        .main-title {
            font-size: 2.5em;
            color: #2c3e50;
            margin-bottom: 15px;
            font-weight: 700;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .main-title i {
            color: #3498db;
            font-size: 0.9em;
        }

        .main-subtitle {
            font-size: 1.2em;
            color: #6c757d;
            margin-bottom: 25px;
            line-height: 1.6;
        }

        .client-info {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            background: linear-gradient(135deg, #e8f5e8, #f0f9ff);
            padding: 12px 20px;
            border-radius: 25px;
            color: #2c3e50;
            font-weight: 600;
            border: 2px solid #28a745;
        }

        .client-info i {
            color: #28a745;
            font-size: 1.2em;
        }

        /* Terms Container */
        .terms-main-container {
            background: rgba(255,255,255,0.98);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 15px 50px rgba(0,0,0,0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255,255,255,0.3);
            position: relative;
            overflow: hidden;
        }

        .terms-main-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
            background-size: 200% 100%;
            animation: gradientShift 3s ease infinite;
        }

        /* Responsive Design for Header */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                text-align: center;
            }

            .logo-text h1 {
                font-size: 24px;
            }

            .progress-steps {
                gap: 20px;
            }

            .step-icon {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }

            .step-text {
                font-size: 11px;
            }

            .main-title {
                font-size: 2em;
                flex-direction: column;
                gap: 10px;
            }

            .welcome-section,
            .terms-main-container {
                padding: 25px;
                margin: 15px;
            }
        }
    </style>
</head>
<body>
    <!-- Professional Header -->
    <div class="professional-header">
        <div class="container-professional">
            <div class="header-content-professional">
                <div class="logo-section-professional">
                    <div class="logo-icon-professional">
                        <i class="fas fa-balance-scale"></i>
                    </div>
                    <div class="logo-text-professional">
                        <h1>TaxFree Pro</h1>
                        <p>שירותי מס מקצועיים ומורשים</p>
                        <div class="license-info">
                            <span class="license-badge">רישיון עו"ד מס' 45821</span>
                        </div>
                    </div>
                </div>
                <div class="credentials-section">
                    <div class="credential-item">
                        <i class="fas fa-certificate"></i>
                        <div class="credential-text">
                            <span class="credential-title">מוסמך ע"י</span>
                            <span class="credential-detail">לשכת עורכי הדין בישראל</span>
                        </div>
                    </div>
                    <div class="credential-item">
                        <i class="fas fa-shield-alt"></i>
                        <div class="credential-text">
                            <span class="credential-title">ביטוח אחריות</span>
                            <span class="credential-detail">עד 5 מיליון ₪</span>
                        </div>
                    </div>
                    <div class="credential-item">
                        <i class="fas fa-phone-volume"></i>
                        <div class="credential-text">
                            <span class="credential-title">ייעוץ משפטי</span>
                            <span class="credential-detail">03-7654321</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Clean Progress Indicator -->
    <div class="progress-container-clean">
        <div class="container-clean">
            <div class="progress-steps-clean">
                <div class="step-clean completed">
                    <div class="step-number">1</div>
                    <div class="step-info">
                        <div class="step-title">פרטים אישיים</div>
                        <div class="step-status">הושלם ✓</div>
                    </div>
                </div>
                <div class="step-clean active">
                    <div class="step-number">2</div>
                    <div class="step-info">
                        <div class="step-title">תנאי שימוש</div>
                        <div class="step-status">בתהליך...</div>
                    </div>
                </div>
                <div class="step-clean">
                    <div class="step-number">3</div>
                    <div class="step-info">
                        <div class="step-title">חישוב מס</div>
                        <div class="step-status">ממתין</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Simple Help Button -->
    <div class="help-button-clean">
        <button class="help-btn-clean" onclick="showHelp()" aria-label="עזרה ותמיכה">
            <i class="fas fa-question-circle"></i>
            <span>עזרה</span>
        </button>
    </div>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Government-Style Header -->
            <div class="government-header">
                <div class="container-government">
                    <div class="gov-header-content">
                        <div class="gov-seal">
                            <i class="fas fa-balance-scale"></i>
                        </div>
                        <div class="gov-title-section">
                            <h1 class="gov-main-title">הסכם שירותי מס מקצועיים</h1>
                            <div class="gov-reference">
                                <span class="ref-label">בהתאם לחוק:</span>
                                <span class="ref-text">מס הכנסה התשכ"א-1961 | תקנות עורכי דין התש"ן-1990</span>
                            </div>
                        </div>
                    </div>

                    <div class="gov-notice">
                        <div class="notice-header">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span>הודעה חשובה</span>
                        </div>
                        <p>מסמך זה מהווה הסכם משפטי מחייב. אנא קרא בעיון את כל הסעיפים לפני החתימה.</p>
                    </div>

                    <div class="client-status" id="clientWelcome">
                        <div class="status-icon">
                            <i class="fas fa-user-check"></i>
                        </div>
                        <span>טוען פרטי לקוח...</span>
                    </div>
                </div>
            </div>

            <!-- Terms of Service Container -->
            <div class="terms-main-container">
                <div class="terms-header-section">
                    <div class="terms-navigation">
                        <button type="button" class="back-btn" onclick="goBackToTerms()">
                            <i class="fas fa-arrow-right"></i>
                            חזור לפרטים אישיים
                        </button>
                        <div class="terms-progress-info">
                            <span class="progress-text" id="progressText">התקדמות: 0 מתוך 6 סעיפים הושלמו</span>
                        </div>
                    </div>

                    <div class="terms-progress-bar">
                        <div class="terms-progress-fill" id="termsProgress"></div>
                    </div>
                </div>

                <div class="terms-container">
                        <div class="terms-progress-bar">
                            <div class="terms-progress-fill" id="termsProgress"></div>
                        </div>
                        <div class="progress-text" id="progressText">התקדמות: 0 מתוך 6 סעיפים הושלמו</div>
                        <!-- Government-Style Content Sections -->
                        <div class="government-content">

                            <!-- Section 1: Service Definition -->
                            <div class="gov-section" id="section-1">
                                <div class="section-header">
                                    <div class="section-number">1</div>
                                    <div class="section-title">
                                        <h2>הגדרת השירות</h2>
                                        <p>שירותי מס מקצועיים ומורשים</p>
                                    </div>
                                </div>

                                <div class="section-content">
                                    <div class="subsection">
                                        <h3>1.1 בסיס חוקי</h3>
                                        <div class="legal-references-gov">
                                            <div class="ref-item">
                                                <span class="ref-code">חוק מס הכנסה התשכ"א-1961</span>
                                                <span class="ref-desc">סעיף 161 - החזרי מס</span>
                                            </div>
                                            <div class="ref-item">
                                                <span class="ref-code">תקנות עורכי דין התש"ן-1990</span>
                                                <span class="ref-desc">תקנה 15א - שכר טרחה</span>
                                            </div>
                                            <div class="ref-item">
                                                <span class="ref-code">חוק הייפוי כוח התשכ"ה-1965</span>
                                                <span class="ref-desc">סעיפים 1-8 - הגדרות וסמכויות</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="subsection">
                                        <h3>1.2 היקף השירות</h3>
                                        <div class="service-list-gov">
                                            <div class="service-item-gov">
                                                <div class="item-marker">א</div>
                                                <div class="item-text">
                                                    <strong>ייעוץ משפטי מקצועי</strong> - בדיקת זכאות להחזר מס
                                                </div>
                                            </div>
                                            <div class="service-item-gov">
                                                <div class="item-marker">ב</div>
                                                <div class="item-text">
                                                    <strong>ניתוח מסמכים</strong> - בחינת תקינות מסמכי מס
                                                </div>
                                            </div>
                                            <div class="service-item-gov">
                                                <div class="item-marker">ג</div>
                                                <div class="item-text">
                                                    <strong>חישוב מס מקצועי</strong> - לפי טבלאות רשמיות 2024-2025
                                                </div>
                                            </div>
                                            <div class="service-item-gov">
                                                <div class="item-marker">ד</div>
                                                <div class="item-text">
                                                    <strong>הגשה דיגיטלית</strong> - הגשת בקשה לרשות המסים
                                                </div>
                                            </div>
                                            <div class="service-item-gov">
                                                <div class="item-marker">ה</div>
                                                <div class="item-text">
                                                    <strong>ייצוג משפטי</strong> - מעקב וטיפול עד קבלת ההחזר
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="subsection">
                                        <h3>1.3 תקנים מקצועיים</h3>
                                        <div class="standards-gov">
                                            <div class="standard-badge">
                                                <i class="fas fa-certificate"></i>
                                                <span>עורך דין מורשה מס' 45821</span>
                                            </div>
                                            <div class="standard-badge">
                                                <i class="fas fa-shield-alt"></i>
                                                <span>ביטוח אחריות מקצועית עד 5M ₪</span>
                                            </div>
                                            <div class="standard-badge">
                                                <i class="fas fa-eye"></i>
                                                <span>פיקוח לשכת עורכי הדין</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Section 2: Pricing and Payment -->
                            <div class="gov-section" id="section-2">
                                <div class="section-header">
                                    <div class="section-number">2</div>
                                    <div class="section-title">
                                        <h2>שכר טרחה ותנאי תשלום</h2>
                                        <p>תמחור שקוף בהתאם לתקנות עורכי דין</p>
                                    </div>
                                </div>

                                <div class="section-content">
                                    <div class="subsection">
                                        <h3>2.1 בסיס משפטי לתמחור</h3>
                                        <div class="legal-references-gov">
                                            <div class="ref-item">
                                                <span class="ref-code">תקנה 15א לתקנות עורכי דין</span>
                                                <span class="ref-desc">שכר טרחה לשירותי מס</span>
                                            </div>
                                            <div class="ref-item">
                                                <span class="ref-code">פסיקת בית המשפט העליון ע"א 2605/05</span>
                                                <span class="ref-desc">Success Fee - שכר טרחה מותנה הצלחה</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="subsection">
                                        <h3>2.2 מבנה התמחור</h3>
                                        <div class="pricing-table-gov">
                                            <div class="pricing-main-row">
                                                <div class="pricing-rate">15%</div>
                                                <div class="pricing-description">
                                                    <div class="pricing-title">שכר טרחה מותנה הצלחה</div>
                                                    <div class="pricing-subtitle">מסכום ההחזר הגולמי (כולל מע"מ)</div>
                                                </div>
                                            </div>

                                            <div class="pricing-conditions-gov">
                                                <div class="condition-positive">
                                                    <i class="fas fa-check"></i>
                                                    <span>ללא דמי פתיחת תיק</span>
                                                </div>
                                                <div class="condition-positive">
                                                    <i class="fas fa-check"></i>
                                                    <span>ללא תשלום מראש</span>
                                                </div>
                                                <div class="condition-positive">
                                                    <i class="fas fa-check"></i>
                                                    <span>תשלום רק לאחר קבלת ההחזר</span>
                                                </div>
                                                <div class="condition-neutral">
                                                    <i class="fas fa-info-circle"></i>
                                                    <span>אין תשלום במקרה של אי-אישור הבקשה</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="subsection">
                                        <h3>2.3 דוגמאות חישוב</h3>
                                        <div class="calculation-table-gov">
                                            <div class="calc-header">
                                                <span>סכום החזר</span>
                                                <span>שכר טרחה</span>
                                                <span>סטטוס</span>
                                            </div>
                                            <div class="calc-row">
                                                <span class="amount">₪10,000</span>
                                                <span class="fee">₪1,500</span>
                                                <span class="status success">מאושר</span>
                                            </div>
                                            <div class="calc-row">
                                                <span class="amount">₪25,000</span>
                                                <span class="fee">₪3,750</span>
                                                <span class="status success">מאושר</span>
                                            </div>
                                            <div class="calc-row">
                                                <span class="amount">₪0</span>
                                                <span class="fee">₪0</span>
                                                <span class="status neutral">לא מאושר</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="subsection">
                                        <h3>2.4 לוחות זמנים</h3>
                                        <div class="timeline-gov">
                                            <div class="timeline-item-gov">
                                                <div class="timeline-period">45 ימים</div>
                                                <div class="timeline-desc">זמן חוקי להחזר מס (סעיף 161א)</div>
                                            </div>
                                            <div class="timeline-item-gov">
                                                <div class="timeline-period">3 ימי עסקים</div>
                                                <div class="timeline-desc">תשלום שכר טרחה לאחר קבלת ההחזר</div>
                                            </div>
                                            <div class="timeline-item-gov">
                                                <div class="timeline-period">14 ימים</div>
                                                <div class="timeline-desc">זכות ביטול (חוק הגנת הצרכן)</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Section 3: Power of Attorney -->
                            <div class="gov-section" id="section-3">
                                <div class="section-header">
                                    <div class="section-number">3</div>
                                    <div class="section-title">
                                        <h2>ייפוי כוח - טופס 8832</h2>
                                        <p>הרשאה חוקית לטיפול בענייני מס</p>
                                    </div>
                                </div>

                                <div class="section-content">
                                    <div class="subsection">
                                        <h3>3.1 בסיס חוקי</h3>
                                        <div class="legal-references-gov">
                                            <div class="ref-item">
                                                <span class="ref-code">חוק הייפוי כוח התשכ"ה-1965</span>
                                                <span class="ref-desc">סעיפים 1-8 - הגדרות וסמכויות</span>
                                            </div>
                                            <div class="ref-item">
                                                <span class="ref-code">טופס 8832 רשמי</span>
                                                <span class="ref-desc">טופס מאושר רשות המסים</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="subsection">
                                        <h3>3.2 היקף הסמכויות</h3>
                                        <div class="authority-scope-gov">
                                            <div class="scope-section authorized">
                                                <div class="scope-header">
                                                    <i class="fas fa-check-circle"></i>
                                                    <h4>סמכויות מורשות</h4>
                                                </div>
                                                <div class="scope-list">
                                                    <div class="scope-item-gov">הגשת בקשה להחזר מס</div>
                                                    <div class="scope-item-gov">קבלת מידע מרשות המסים</div>
                                                    <div class="scope-item-gov">מעקב אחר סטטוס הבקשה</div>
                                                    <div class="scope-item-gov">קבלת החלטות והודעות</div>
                                                    <div class="scope-item-gov">ייצוג בפגישות</div>
                                                </div>
                                            </div>

                                            <div class="scope-section limited">
                                                <div class="scope-header">
                                                    <i class="fas fa-shield-alt"></i>
                                                    <h4>הגבלות והגנות</h4>
                                                </div>
                                                <div class="scope-list">
                                                    <div class="scope-item-gov">מוגבל לשנת המס הנוכחית בלבד</div>
                                                    <div class="scope-item-gov">ללא גישה לפרטים אישיים נוספים</div>
                                                    <div class="scope-item-gov">ללא סמכות חתימה על מסמכים אחרים</div>
                                                    <div class="scope-item-gov">ללא גישה לחשבונות בנק</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="subsection">
                                        <h3>3.3 תוקף ייפוי הכוח</h3>
                                        <div class="validity-table-gov">
                                            <div class="validity-row">
                                                <div class="validity-label">תחילת תוקף</div>
                                                <div class="validity-value">מיום חתימת המסמך</div>
                                            </div>
                                            <div class="validity-row">
                                                <div class="validity-label">סיום תוקף</div>
                                                <div class="validity-value">השלמת הטיפול או 31/12/2025</div>
                                            </div>
                                            <div class="validity-row">
                                                <div class="validity-label">זכות ביטול</div>
                                                <div class="validity-value">בכל עת בהודעה בכתב</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="subsection">
                                        <h3>3.4 צפייה בטופס</h3>
                                        <div class="form-access-gov">
                                            <div class="form-info">
                                                <i class="fas fa-file-alt"></i>
                                                <div class="form-details">
                                                    <div class="form-title">טופס 8832 - ייפוי כוח</div>
                                                    <div class="form-desc">טופס רשמי של רשות המסים</div>
                                                </div>
                                            </div>
                                            <button class="view-form-btn" onclick="togglePowerOfAttorney()">
                                                <i class="fas fa-eye"></i>
                                                <span>צפייה בטופס</span>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                                <div id="powerOfAttorneyForm" style="display: none;">
                                    <div class="legal-explanation">
                                        <div class="legal-title">⚖️ הסברים משפטיים לטופס</div>
                                        <div class="legal-content">
                                            <ul>
                                                <li><strong>סעיף 1-2:</strong> זיהוי הצדדים - המרשה (אתה) והמורשה (TaxFree Pro)</li>
                                                <li><strong>סעיף 3:</strong> היקף ההרשאות - מוגבל לענייני החזר מס בלבד</li>
                                                <li><strong>סעיף 4:</strong> הגבלות - אין הרשאה לחתימה על התחייבויות כספיות</li>
                                                <li><strong>סעיף 5:</strong> תוקף זמני - מוגבל בזמן ובהיקף</li>
                                                <li><strong>סעיף 6:</strong> זכות ביטול - ניתן לבטל בכל עת בהודעה בכתב</li>
                                            </ul>
                                        </div>
                                    </div>
                                <div class="form-8832-container">
                                    <div class="form-header">
                                        <div class="form-logo">
                                            <div class="israel-emblem">🇮🇱</div>
                                            <div class="form-title">
                                                <h3>מדינת ישראל</h3>
                                                <h4>רשות המסים</h4>
                                                <h5>טופס 8832 - ייפוי כוח</h5>
                                            </div>
                                        </div>
                                        <div class="form-number">
                                            <span>מס' טופס: 8832</span>
                                            <span>גרסה: 01/2024</span>
                                        </div>
                                    </div>

                                    <div class="form-content">
                                        <div class="form-section-title">פרטי הנותן ייפוי הכוח (המרשה)</div>

                                        <div class="form-fields">
                                            <div class="form-field">
                                                <label>שם פרטי ומשפחה:</label>
                                                <div class="field-value" id="poa-fullName">יתמלא אוטומטית</div>
                                            </div>
                                            <div class="form-field">
                                                <label>מספר זהות:</label>
                                                <div class="field-value" id="poa-idNumber">יתמלא אוטומטית</div>
                                            </div>
                                            <div class="form-field">
                                                <label>כתובת:</label>
                                                <div class="field-value">לפי הרישומים ברשות המסים</div>
                                            </div>
                                            <div class="form-field">
                                                <label>טלפון:</label>
                                                <div class="field-value" id="poa-phone">יתמלא אוטומטית</div>
                                            </div>
                                            <div class="form-field">
                                                <label>דואר אלקטרוני:</label>
                                                <div class="field-value" id="poa-email">יתמלא אוטומטית</div>
                                            </div>
                                        </div>

                                        <div class="form-section-title">פרטי המקבל ייפוי הכוח (המורשה)</div>

                                        <div class="form-fields">
                                            <div class="form-field">
                                                <label>שם החברה/עורך דין:</label>
                                                <div class="field-value">TaxFree Pro - שירותי מס מתקדמים בע"מ</div>
                                            </div>
                                            <div class="form-field">
                                                <label>מספר רישיון:</label>
                                                <div class="field-value">רישיון עורך דין מס' 12345</div>
                                            </div>
                                            <div class="form-field">
                                                <label>כתובת:</label>
                                                <div class="field-value">רחוב הטכנולוגיה 1, תל אביב</div>
                                            </div>
                                            <div class="form-field">
                                                <label>טלפון:</label>
                                                <div class="field-value">03-1234567</div>
                                            </div>
                                        </div>

                                        <div class="form-section-title">היקף ייפוי הכוח</div>

                                        <div class="authorization-scope">
                                            <p><strong>אני מרשה בזה את המורשה לפעול בשמי ובמקומי בכל הנושאים הבאים:</strong></p>

                                            <div class="scope-items">
                                                <div class="scope-item">
                                                    <span class="scope-number">1.</span>
                                                    <div class="scope-content">
                                                        <strong>הגשת בקשה להחזר מס הכנסה</strong> - לשנת המס הרלוונטית, כולל מילוי וחתימה על כל הטפסים הנדרשים
                                                    </div>
                                                </div>

                                                <div class="scope-item">
                                                    <span class="scope-number">2.</span>
                                                    <div class="scope-content">
                                                        <strong>קבלת מידע ממס הכנסה</strong> - צפייה בנתונים, קבלת אישורים ומסמכים הנוגעים לבקשת החזר המס
                                                    </div>
                                                </div>

                                                <div class="scope-item">
                                                    <span class="scope-number">3.</span>
                                                    <div class="scope-content">
                                                        <strong>מעקב וטיפול בבקשה</strong> - בדיקת סטטוס, מתן הסברים נוספים, הגשת מסמכים משלימים
                                                    </div>
                                                </div>

                                                <div class="scope-item">
                                                    <span class="scope-number">4.</span>
                                                    <div class="scope-content">
                                                        <strong>תיקון ועדכון נתונים</strong> - במידת הצורך, לצורך השלמת הבקשה או תיקון טעויות
                                                    </div>
                                                </div>

                                                <div class="scope-item">
                                                    <span class="scope-number">5.</span>
                                                    <div class="scope-content">
                                                        <strong>קבלת התכתבויות</strong> - מרשות המסים בנושא בקשת החזר המס, כולל החלטות ודרישות
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-section-title">הגבלות ייפוי הכוח</div>

                                        <div class="limitations">
                                            <div class="limitation-item">
                                                <span class="limitation-icon">⚠️</span>
                                                <div class="limitation-text">
                                                    ייפוי כוח זה מוגבל <strong>אך ורק</strong> לטיפול בבקשת החזר מס הכנסה לשנת המס הנוכחית
                                                </div>
                                            </div>

                                            <div class="limitation-item">
                                                <span class="limitation-icon">⚠️</span>
                                                <div class="limitation-text">
                                                    ייפוי הכוח <strong>אינו כולל</strong> הרשאה לטיפול בנושאי מס אחרים (מע"מ, מס רכוש, וכו')
                                                </div>
                                            </div>

                                            <div class="limitation-item">
                                                <span class="limitation-icon">⚠️</span>
                                                <div class="limitation-text">
                                                    ייפוי הכוח <strong>אינו כולל</strong> הרשאה לחתימה על הסכמים או התחייבויות כספיות
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-section-title">תוקף ייפוי הכוח</div>

                                        <div class="validity-section">
                                            <div class="validity-item">
                                                <label>תאריך תחילה:</label>
                                                <span class="validity-date" id="poa-startDate">תאריך החתימה</span>
                                            </div>
                                            <div class="validity-item">
                                                <label>תאריך סיום:</label>
                                                <span class="validity-date">31/12/2025 או עם השלמת הטיפול בבקשה</span>
                                            </div>
                                        </div>

                                        <!-- Official Disclaimer Section -->
                                        <div class="official-disclaimer-section">
                                            <div class="disclaimer-header">
                                                <div class="disclaimer-icon">⚖️</div>
                                                <h4>הצהרה רשמית נוספת</h4>
                                            </div>
                                            <div class="disclaimer-content">
                                                <p><strong>אני מעניק/ה בזה ייפוי כוח ל-TaxFree Pro</strong> ושל כל עובדיו הרשאים לייצג נישומים על פי כל דין להיות בא כוחי/נו ולפעול בשמי/נו בכל פעולה שהינה בסמכותו לפי החוק.</p>

                                                <p>בקשר לכל אותן הפעולות שאני/אנחנו רשאי/ים וחייב/ים לעשותן לפי פקודת מס הכנסה, חוק מע"מ וחוק מס רכוש לרבות פשרה.</p>

                                                <p>בנוסף לכך, לאפשר מתן הוראות לחייב את חשבון הבנק שלי/שלנו לטובת רשות המסים בהתאם לדיווחים שלי/שלנו.</p>

                                                <p><strong>ייפוי כוח זה יהיה תקף לתקופה של 24 חודשים בלבד</strong> (מיום קליטתו או מיום חתימתו, לפי המאוחר) או עד שאודיעכם/נודיעכם על ביטולו, לפי המוקדם, כל עוד הוא מיועד לטיפול בהחזר מס ליחיד שאינו חייב בהגשת דו"ח (סוג תיק 9.1).</p>

                                                <p>במקרים אחרים ייפוי כוח זה תקף כל עוד לא אודיעכם/נודיעכם על ביטולו.</p>

                                                <p><strong>כל פעולה שתעשה ע"י המייצג מחייבת/מזכה לפי העניין, אותי/אותנו.</strong></p>

                                                <p><strong>האחריות שלא לחרוג מהרשאה זו היא על המייצג.</strong></p>

                                                <p><strong>גם לתשומת לבך/לבכם:</strong> אם הטופס חתום ע"י "בן הזוג הרשום" בלבד, ללא חתימת בן הזוג השני, יראו במס הכנסה ובניכויים את החותם כמי שהצהיר שבידו ייפוי כוח מבן זוגו לחתום בשמו, וזאת בהתאם להוראות ס' 144 לפקודת מס הכנסה.</p>

                                                <p><strong>אני/אנחנו מאשר/ים לרשות המסים לשלוח הודעות באמצעות מסרון (SMS) או לתיבת הדואר האלקטרוני.</strong></p>
                                            </div>
                                        </div>

                                        <div class="form-footer">
                                            <div class="signature-area">
                                                <div class="signature-field">
                                                    <label>תאריך:</label>
                                                    <span class="signature-line" id="poa-signatureDate">יתמלא בחתימה</span>
                                                </div>
                                                <div class="signature-field">
                                                    <label>חתימת המרשה:</label>
                                                    <span class="signature-line">יתמלא בחתימה דיגיטלית</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="important-note">
                                    <strong>הערה חשובה:</strong> טופס זה יוגש אוטומטית לרשות המסים יחד עם בקשת החזר המס.
                                    ייפוי הכוח יבוטל אוטומטית עם השלמת הטיפול בבקשה.
                                </div>
                            </div>
                        </div>

                        <!-- Professional Summary Section -->
                        <div class="professional-summary">
                            <div class="summary-header">
                                <div class="summary-icon">
                                    <i class="fas fa-gavel"></i>
                                </div>
                                <div class="summary-content">
                                    <h3>סיכום משפטי</h3>
                                    <p>מסמך זה מהווה הסכם משפטי מחייב. קריאתו וחתימתו מהווים הסכמה מלאה לכל התנאים המפורטים לעיל.</p>
                                </div>
                            </div>
                            <div class="legal-declaration">
                                <div class="declaration-text">
                                    <p><strong>הצהרה משפטית:</strong> אני מאשר כי קראתי והבנתי את כל סעיפי ההסכם, מסכים לתנאי שכר הטרחה,
                                    ומעניק ייפוי כוח מוגבל לטיפול בבקשת החזר המס בלבד.</p>
                                </div>
                            </div>
                        </div>

                            <!-- Section 4: Privacy and Compliance -->
                            <div class="gov-section" id="section-4">
                                <div class="section-header">
                                    <div class="section-number">4</div>
                                    <div class="section-title">
                                        <h2>הגנת פרטיות וציות רגולטורי</h2>
                                        <p>אבטחת מידע וציות לחוק</p>
                                    </div>
                                </div>

                                <div class="section-content">
                                    <div class="subsection">
                                        <h3>4.1 בסיס חוקי</h3>
                                        <div class="legal-references-gov">
                                            <div class="ref-item">
                                                <span class="ref-code">חוק הגנת הפרטיות התשמ"א-1981</span>
                                                <span class="ref-desc">הגנה על מידע אישי</span>
                                            </div>
                                            <div class="ref-item">
                                                <span class="ref-code">תקנות עורכי דין התש"ן-1990</span>
                                                <span class="ref-desc">חובת סודיות מקצועית</span>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="subsection">
                                        <h3>4.2 אמצעי הגנת מידע</h3>
                                        <div class="security-measures-gov">
                                            <div class="security-item">
                                                <div class="security-icon">
                                                    <i class="fas fa-lock"></i>
                                                </div>
                                                <div class="security-details">
                                                    <div class="security-title">הצפנת AES-256</div>
                                                    <div class="security-desc">הצפנה ברמה בנקאית</div>
                                                </div>
                                            </div>
                                            <div class="security-item">
                                                <div class="security-icon">
                                                    <i class="fas fa-server"></i>
                                                </div>
                                                <div class="security-details">
                                                    <div class="security-title">אחסון מאובטח</div>
                                                    <div class="security-desc">שרתים בישראל בלבד</div>
                                                </div>
                                            </div>
                                            <div class="security-item">
                                                <div class="security-icon">
                                                    <i class="fas fa-user-shield"></i>
                                                </div>
                                                <div class="security-details">
                                                    <div class="security-title">גישה מוגבלת</div>
                                                    <div class="security-desc">אימות דו-שלבי</div>
                                                </div>
                                            </div>
                                            <div class="security-item">
                                                <div class="security-icon">
                                                    <i class="fas fa-clock"></i>
                                                </div>
                                                <div class="security-details">
                                                    <div class="security-title">מחיקה אוטומטית</div>
                                                    <div class="security-desc">לאחר 7 שנים</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="subsection">
                                        <h3>4.3 ציות רגולטורי</h3>
                                        <div class="compliance-table-gov">
                                            <div class="compliance-row">
                                                <div class="compliance-type">רישיון מקצועי</div>
                                                <div class="compliance-details">עורך דין מורשה מס' 45821</div>
                                                <div class="compliance-status active">פעיל</div>
                                            </div>
                                            <div class="compliance-row">
                                                <div class="compliance-type">ביטוח אחריות</div>
                                                <div class="compliance-details">כיסוי עד 5 מיליון ₪</div>
                                                <div class="compliance-status active">בתוקף</div>
                                            </div>
                                            <div class="compliance-row">
                                                <div class="compliance-type">פיקוח מקצועי</div>
                                                <div class="compliance-details">לשכת עורכי הדין</div>
                                                <div class="compliance-status active">מפוקח</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="subsection">
                                        <h3>4.4 זכויות הלקוח</h3>
                                        <div class="rights-gov">
                                            <div class="right-item-gov">
                                                <i class="fas fa-eye"></i>
                                                <span>זכות עיון במידע האישי</span>
                                            </div>
                                            <div class="right-item-gov">
                                                <i class="fas fa-edit"></i>
                                                <span>זכות תיקון מידע שגוי</span>
                                            </div>
                                            <div class="right-item-gov">
                                                <i class="fas fa-times-circle"></i>
                                                <span>זכות ביטול הסכם תוך 14 ימים</span>
                                            </div>
                                            <div class="right-item-gov">
                                                <i class="fas fa-balance-scale"></i>
                                                <span>זכות הגשת תלונה ללשכת עורכי הדין</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Client Rights -->
                        <div class="terms-section" id="rights-section">
                            <div class="terms-header">
                                <div class="terms-icon">👤</div>
                                <h4>זכויות הלקוח</h4>
                            </div>
                            <div class="terms-content">
                                <ul>
                                    <li><strong>זכות ביטול</strong> - ניתן לבטל את השירות תוך 14 ימים</li>
                                    <li><strong>זכות עיון</strong> - צפייה במידע האישי בכל עת</li>
                                    <li><strong>זכות תיקון</strong> - עדכון נתונים שגויים</li>
                                    <li><strong>זכות מחיקה</strong> - מחיקת המידע לפי בקשה</li>
                                    <li><strong>תמיכה מקצועית</strong> - זמינות טלפונית ובאימייל</li>
                                    <li><strong>החזר כספי</strong> - במקרה של אי הצלחה בהגשה</li>
                                </ul>
                            </div>
                        </div>

                        <!-- Power of Attorney Section - Full Form 8832 -->
                        <div class="power-of-attorney-final-section">
                            <h4>📜 ייפוי כוח רשמי - טופס 8832</h4>
                            <div class="legal-explanation">
                                <div class="legal-title">⚖️ אישור ייפוי הכוח</div>
                                <div class="legal-content">
                                    <p><strong>בחתימתך למטה, אתה מאשר ומעניק ייפוי כוח רשמי (טופס 8832) ל-TaxFree Pro לטיפול בבקשת החזר המס שלך.</strong></p>
                                    <p><strong>להלן הטופס המלא שיוגש לרשות המסים:</strong></p>
                                </div>
                            </div>

                            <!-- Full Form 8832 Display -->
                            <div class="form-8832-container" id="fullForm8832">
                                <div class="form-header">
                                    <div class="form-logo">
                                        <div class="israel-emblem">🇮🇱</div>
                                        <div class="form-title">
                                            <h3>מדינת ישראל</h3>
                                            <h4>רשות המסים</h4>
                                            <h5>טופס 8832 - ייפוי כוח</h5>
                                        </div>
                                    </div>
                                    <div class="form-number">
                                        <span>מס' טופס: 8832</span>
                                        <span>גרסה: 01/2024</span>
                                    </div>
                                </div>

                                <div class="form-content">
                                    <div class="form-section-title">פרטי הנותן ייפוי הכוח (המרשה)</div>

                                    <div class="form-fields">
                                        <div class="form-field">
                                            <label>שם פרטי ומשפחה:</label>
                                            <div class="field-value" id="poa-fullName-final">יתמלא אוטומטית</div>
                                        </div>
                                        <div class="form-field">
                                            <label>מספר זהות:</label>
                                            <div class="field-value" id="poa-idNumber-final">יתמלא אוטומטית</div>
                                        </div>
                                        <div class="form-field">
                                            <label>כתובת:</label>
                                            <div class="field-value">לפי הרישומים ברשות המסים</div>
                                        </div>
                                        <div class="form-field">
                                            <label>טלפון:</label>
                                            <div class="field-value" id="poa-phone-final">יתמלא אוטומטית</div>
                                        </div>
                                        <div class="form-field">
                                            <label>דואר אלקטרוני:</label>
                                            <div class="field-value" id="poa-email-final">יתמלא אוטומטית</div>
                                        </div>
                                    </div>

                                    <div class="form-section-title">פרטי המקבל ייפוי הכוח (המורשה)</div>

                                    <div class="form-fields">
                                        <div class="form-field">
                                            <label>שם החברה/עורך דין:</label>
                                            <div class="field-value">TaxFree Pro - שירותי מס מתקדמים בע"מ</div>
                                        </div>
                                        <div class="form-field">
                                            <label>מספר רישיון:</label>
                                            <div class="field-value">רישיון עורך דין מס' 12345</div>
                                        </div>
                                        <div class="form-field">
                                            <label>כתובת:</label>
                                            <div class="field-value">רחוב הטכנולוגיה 1, תל אביב</div>
                                        </div>
                                        <div class="form-field">
                                            <label>טלפון:</label>
                                            <div class="field-value">03-1234567</div>
                                        </div>
                                    </div>

                                    <div class="form-section-title">היקף ייפוי הכוח</div>

                                    <div class="authorization-scope">
                                        <p><strong>אני מרשה בזה את המורשה לפעול בשמי ובמקומי בכל הנושאים הבאים:</strong></p>

                                        <div class="scope-items">
                                            <div class="scope-item">
                                                <span class="scope-number">1.</span>
                                                <div class="scope-content">
                                                    <strong>הגשת בקשה להחזר מס הכנסה</strong> - לשנת המס הרלוונטית, כולל מילוי וחתימה על כל הטפסים הנדרשים
                                                </div>
                                            </div>

                                            <div class="scope-item">
                                                <span class="scope-number">2.</span>
                                                <div class="scope-content">
                                                    <strong>קבלת מידע ממס הכנסה</strong> - צפייה בנתונים, קבלת אישורים ומסמכים הנוגעים לבקשת החזר המס
                                                </div>
                                            </div>

                                            <div class="scope-item">
                                                <span class="scope-number">3.</span>
                                                <div class="scope-content">
                                                    <strong>מעקב וטיפול בבקשה</strong> - בדיקת סטטוס, מתן הסברים נוספים, הגשת מסמכים משלימים
                                                </div>
                                            </div>

                                            <div class="scope-item">
                                                <span class="scope-number">4.</span>
                                                <div class="scope-content">
                                                    <strong>תיקון ועדכון נתונים</strong> - במידת הצורך, לצורך השלמת הבקשה או תיקון טעויות
                                                </div>
                                            </div>

                                            <div class="scope-item">
                                                <span class="scope-number">5.</span>
                                                <div class="scope-content">
                                                    <strong>קבלת התכתבויות</strong> - מרשות המסים בנושא בקשת החזר המס, כולל החלטות ודרישות
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-section-title">הגבלות ייפוי הכוח</div>

                                    <div class="limitations">
                                        <div class="limitation-item">
                                            <span class="limitation-icon">⚠️</span>
                                            <div class="limitation-text">
                                                ייפוי כוח זה מוגבל <strong>אך ורק</strong> לטיפול בבקשת החזר מס הכנסה לשנת המס הנוכחית
                                            </div>
                                        </div>

                                        <div class="limitation-item">
                                            <span class="limitation-icon">⚠️</span>
                                            <div class="limitation-text">
                                                ייפוי הכוח <strong>אינו כולל</strong> הרשאה לטיפול בנושאי מס אחרים (מע"מ, מס רכוש, וכו')
                                            </div>
                                        </div>

                                        <div class="limitation-item">
                                            <span class="limitation-icon">⚠️</span>
                                            <div class="limitation-text">
                                                ייפוי הכוח <strong>אינו כולל</strong> הרשאה לחתימה על הסכמים או התחייבויות כספיות
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-section-title">תוקף ייפוי הכוח</div>

                                    <div class="validity-section">
                                        <div class="validity-item">
                                            <label>תאריך תחילה:</label>
                                            <span class="validity-date" id="poa-startDate-final">תאריך החתימה</span>
                                        </div>
                                        <div class="validity-item">
                                            <label>תאריך סיום:</label>
                                            <span class="validity-date">31/12/2025 או עם השלמת הטיפול בבקשה</span>
                                        </div>
                                    </div>

                                    <!-- Official Disclaimer Section -->
                                    <div class="official-disclaimer-section">
                                        <div class="disclaimer-header">
                                            <div class="disclaimer-icon">⚖️</div>
                                            <h4>הצהרה רשמית נוספת</h4>
                                        </div>
                                        <div class="disclaimer-content">
                                            <p><strong>אני מעניק/ה בזה ייפוי כוח ל-TaxFree Pro</strong> ושל כל עובדיו הרשאים לייצג נישומים על פי כל דין להיות בא כוחי/נו ולפעול בשמי/נו בכל פעולה שהינה בסמכותו לפי החוק.</p>

                                            <p>בקשר לכל אותן הפעולות שאני/אנחנו רשאי/ים וחייב/ים לעשותן לפי פקודת מס הכנסה, חוק מע"מ וחוק מס רכוש לרבות פשרה.</p>

                                            <p>בנוסף לכך, לאפשר מתן הוראות לחייב את חשבון הבנק שלי/שלנו לטובת רשות המסים בהתאם לדיווחים שלי/שלנו.</p>

                                            <p><strong>ייפוי כוח זה יהיה תקף לתקופה של 24 חודשים בלבד</strong> (מיום קליטתו או מיום חתימתו, לפי המאוחר) או עד שאודיעכם/נודיעכם על ביטולו, לפי המוקדם, כל עוד הוא מיועד לטיפול בהחזר מס ליחיד שאינו חייב בהגשת דו"ח (סוג תיק 9.1).</p>

                                            <p>במקרים אחרים ייפוי כוח זה תקף כל עוד לא אודיעכם/נודיעכם על ביטולו.</p>

                                            <p><strong>כל פעולה שתעשה ע"י המייצג מחייבת/מזכה לפי העניין, אותי/אותנו.</strong></p>

                                            <p><strong>האחריות שלא לחרוג מהרשאה זו היא על המייצג.</strong></p>

                                            <p><strong>גם לתשומת לבך/לבכם:</strong> אם הטופס חתום ע"י "בן הזוג הרשום" בלבד, ללא חתימת בן הזוג השני, יראו במס הכנסה ובניכויים את החותם כמי שהצהיר שבידו ייפוי כוח מבן זוגו לחתום בשמו, וזאת בהתאם להוראות ס' 144 לפקודת מס הכנסה.</p>

                                            <p><strong>אני/אנחנו מאשר/ים לרשות המסים לשלוח הודעות באמצעות מסרון (SMS) או לתיבת הדואר האלקטרוני.</strong></p>
                                        </div>
                                    </div>

                                    <div class="form-footer">
                                        <div class="signature-area">
                                            <div class="signature-field">
                                                <label>תאריך:</label>
                                                <span class="signature-line" id="poa-signatureDate-final">יתמלא בחתימה</span>
                                            </div>
                                            <div class="ultra-signature-container">
                                                <!-- Premium Header -->
                                                <div class="signature-header-premium">
                                                    <div class="header-content">
                                                        <div class="signature-title-premium">
                                                            <div class="title-icon-wrapper">
                                                                <i class="fas fa-signature signature-icon-premium"></i>
                                                            </div>
                                                            <div class="title-text">
                                                                <h3>חתימה דיגיטלית</h3>
                                                                <p>טופס 8832 - יפוי כח למס הכנסה</p>
                                                            </div>
                                                        </div>
                                                        <div class="signature-date-premium">
                                                            <div class="date-wrapper">
                                                                <i class="fas fa-calendar-check date-icon-premium"></i>
                                                                <div class="date-info">
                                                                    <span class="date-label-premium">תאריך חתימה</span>
                                                                    <span class="date-value-premium" id="poa-signatureDate-final">__/__/____</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <!-- Ultra Signature Pad -->
                                                <div class="signature-workspace-clean">
                                                    <div class="signature-instructions-clean">
                                                        <div class="instruction-header">
                                                            <i class="fas fa-pen-fancy instruction-icon"></i>
                                                            <span class="instruction-title">הוראות חתימה דיגיטלית</span>
                                                        </div>
                                                        <div class="instruction-methods">
                                                            <div class="method-item">
                                                                <i class="fas fa-desktop"></i>
                                                                <span>במחשב: לחץ וגרור עם העכבר</span>
                                                            </div>
                                                            <div class="method-item">
                                                                <i class="fas fa-mobile-alt"></i>
                                                                <span>במובייל: גע וגרור עם האצבע</span>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div class="signature-pad-clean">
                                                        <div class="signature-area-clean">
                                                            <!-- Clean signature lines -->
                                                            <div class="signature-lines-clean">
                                                                <div class="line-clean line-top-clean"></div>
                                                                <div class="line-clean line-main-clean"></div>
                                                                <div class="line-clean line-bottom-clean"></div>
                                                            </div>

                                                            <!-- Canvas with clean styling -->
                                                            <canvas id="digitalSignatureCanvas"
                                                                    width="450"
                                                                    height="140"
                                                                    class="signature-canvas-clean"
                                                                    onmousedown="startSign(event)"
                                                                    onmousemove="drawSign(event)"
                                                                    onmouseup="stopSign()"
                                                                    onmouseout="stopSign()"
                                                                    ontouchstart="startSign(event)"
                                                                    ontouchmove="drawSign(event)"
                                                                    ontouchend="stopSign()"></canvas>

                                                            <!-- Clean prompt -->
                                                            <div class="signature-prompt-clean" id="signaturePrompt">
                                                                <div class="prompt-content-clean">
                                                                    <div class="prompt-icon-clean">
                                                                        <i class="fas fa-signature"></i>
                                                                    </div>
                                                                    <div class="prompt-text-clean">
                                                                        <div class="prompt-main-clean">חתום כאן</div>
                                                                        <div class="prompt-sub-clean">לחץ וגרור לחתימה</div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- Clean Controls -->
                                                    <div class="signature-controls-clean">
                                                        <div class="control-left-clean">
                                                            <button type="button" onclick="clearSign()" class="clear-btn-clean">
                                                                <i class="fas fa-undo-alt"></i>
                                                                <span>נקה חתימה</span>
                                                            </button>
                                                        </div>

                                                        <div class="control-right-clean">
                                                            <div class="status-container-clean">
                                                                <div class="status-indicator-clean">
                                                                    <div class="status-icon-clean" id="statusIcon">
                                                                        <i class="fas fa-circle"></i>
                                                                    </div>
                                                                    <div class="status-text-clean">
                                                                        <span class="status-main-clean" id="signatureStatus">ממתין לחתימה</span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="important-note">
                                <strong>הערה חשובה:</strong> טופס זה יוגש אוטומטית לרשות המסים יחד עם בקשת החזר המס.
                                ייפוי הכוח יבוטל אוטומטית עם השלמת הטיפול בבקשה.
                            </div>

                            <div class="form-actions">
                                <button class="expand-btn" onclick="printPowerOfAttorney(event)">🖨️ הדפס טופס 8832</button>
                            </div>
                        </div>

                        <!-- Comprehensive Agreement Section -->
                        <div class="comprehensive-agreement-section">
                            <h4>📋 הצהרה והסכמה מקיפה</h4>

                            <div class="legal-explanation">
                                <div class="legal-title">⚖️ הצהרת הלקוח המלאה</div>
                                <div class="legal-content">
                                    <p><strong>בסימון התיבה למטה, אני מצהיר/ה ומאשר/ת בזה כדלקמן:</strong></p>
                                </div>
                            </div>

                            <div class="comprehensive-agreement-content">
                                <div class="agreement-section-detailed">
                                    <h5>🔍 לגבי השירות המקצועי:</h5>
                                    <ul>
                                        <li>קראתי, הבנתי ומאשר/ת את תיאור השירות המקצועי של TaxFree Pro</li>
                                        <li>אני מבין/ה שהשירות כולל ייעוץ מס מקצועי על ידי עורכי דין מורשים</li>
                                        <li>אני מודע/ת לכך שהשירות כפוף לפיקוח לשכת עורכי הדין בישראל</li>
                                        <li>אני מבין/ה את התחייבויות המקצועיות: נאמנות, זהירות, דיווח וסודיות</li>
                                    </ul>
                                </div>

                                <div class="agreement-section-detailed">
                                    <h5>💰 לגבי מבנה התמחור:</h5>
                                    <ul>
                                        <li>אני מסכים/ה למבנה התמחור של 15% מגובה ההחזר (כולל מע"מ)</li>
                                        <li>אני מבין/ה שהתשלום יבוצע רק לאחר קבלת ההחזר בפועל</li>
                                        <li>אני מודע/ת לזמני החזר החוקיים: 45 ימים סטנדרטי, עד 75 ימים במקרים מורכבים</li>
                                        <li>אני מבין/ה שאין תשלום במקרה של אי אישור הבקשה על ידי רשות המסים</li>
                                        <li>אני מודע/ת לזכותי לביטול ההסכם תוך 14 ימים ללא עלות</li>
                                    </ul>
                                </div>

                                <div class="agreement-section-detailed">
                                    <h5>📜 לגבי ייפוי הכוח (טופס 8832):</h5>
                                    <ul>
                                        <li>אני מעניק/ה בזה ייפוי כוח רשמי ל-TaxFree Pro לטיפול בבקשת החזר המס</li>
                                        <li>אני מבין/ה את היקף ההרשאות: הגשה, קבלת מידע, מעקב, תיקונים והתכתבויות</li>
                                        <li>אני מודע/ת להגבלות: מוגבל רק לבקשת החזר המס הנוכחית</li>
                                        <li>אני מבין/ה שייפוי הכוח תקף עד להשלמת הטיפול או עד 31/12/2025</li>
                                        <li>אני מודע/ת לזכותי לבטל את ייפוי הכוח בכל עת בהודעה בכתב</li>
                                    </ul>
                                </div>

                                <div class="agreement-section-detailed">
                                    <h5>🔒 לגבי הגנת פרטיות ועיבוד מידע:</h5>
                                    <ul>
                                        <li>אני מסכים/ה לעיבוד המידע האישי שלי לצורך מתן השירות</li>
                                        <li>אני מבין/ה שהמידע יישמר במערכות מאובטחות בישראל בלבד</li>
                                        <li>אני מודע/ת לזכויותיי: עיון, תיקון, מחיקה והעברת נתונים</li>
                                        <li>אני מבין/ה שהמידע לא יועבר לצדדים שלישיים ללא הסכמתי</li>
                                        <li>אני מודע/ת למחיקה אוטומטית של הנתונים לאחר 7 שנים</li>
                                    </ul>
                                </div>

                                <div class="agreement-section-detailed">
                                    <h5>⚖️ לגבי ציות רגולטורי ומשפטי:</h5>
                                    <ul>
                                        <li>אני מאשר/ת שהשירות ניתן על ידי עורכי דין מורשים ברישיון פעיל</li>
                                        <li>אני מבין/ה שהשירות פועל בהתאם לחוק מס הכנסה וחוק הגנת הפרטיות</li>
                                        <li>אני מודע/ת לכיסוי ביטוח האחריות המקצועית של עד 2 מיליון ₪</li>
                                        <li>אני מבין/ה את זכותי להגיש תלונה ללשכת עורכי הדין במקרה הצורך</li>
                                    </ul>
                                </div>

                                <div class="agreement-section-detailed">
                                    <h5>👤 לגבי זכויות הלקוח:</h5>
                                    <ul>
                                        <li>אני מכיר/ה בזכויותיי המלאות כלקוח בהתאם לחוק הגנת הצרכן</li>
                                        <li>אני מבין/ה את זכותי לקבל תמיכה מקצועית לאורך כל התהליך</li>
                                        <li>אני מודע/ת לזכותי לקבל עדכונים שוטפים על התקדמות הבקשה</li>
                                        <li>אני מבין/ה את זכותי להחזר כספי במקרה של אי הצלחה בהגשה</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="final-agreement-checkbox">
                                <label class="comprehensive-agreement-item">
                                    <input type="checkbox" id="comprehensiveAgreement" required>
                                    <span class="comprehensive-checkmark"></span>
                                    <div class="agreement-text">
                                        <strong>אני מצהיר/ה בזה שקראתי בעיון, הבנתי במלואם ומאשר/ת את כל התנאים, ההסברים המשפטיים וההצהרות המפורטות לעיל.
                                        אני מעניק/ה ייפוי כוח רשמי (טופס 8832) ל-TaxFree Pro לטיפול בבקשת החזר המס שלי בהתאם לכל האמור לעיל,
                                        ואני מסכים/ה לכל תנאי השירות והתמחור כמפורט.</strong>
                                    </div>
                                </label>
                            </div>
                        </div>

                        <!-- Agreement Confirmation -->
                        <div class="unified-agreement-section">
                            <div class="agreement-header-unified">
                                <div class="agreement-icon-unified">
                                    <i class="fas fa-shield-check"></i>
                                </div>
                                <div class="agreement-title-unified">
                                    <h4>אישור סופי והסכמה מקיפה</h4>
                                    <p>אישור כל התנאים הנדרשים לביצוע השירות המקצועי</p>
                                </div>
                            </div>

                            <div class="unified-checkbox-wrapper">
                                <label class="unified-checkbox-label">
                                    <input type="checkbox" id="unifiedAgreement" class="unified-checkbox-input" required>
                                    <div class="unified-checkmark-container">
                                        <div class="unified-checkmark-circle">
                                            <i class="fas fa-check unified-checkmark-icon"></i>
                                        </div>
                                    </div>
                                    <div class="unified-agreement-content">
                                        <div class="agreement-main-title">
                                            אני מאשר/ת ומסכים/ה לכל התנאים הבאים:
                                        </div>
                                        <div class="agreement-items-list">
                                            <div class="agreement-item-unified">
                                                <i class="fas fa-check-circle item-icon"></i>
                                                <span>תנאי השירות ומדיניות הפרטיות של TaxFree Pro</span>
                                            </div>
                                            <div class="agreement-item-unified">
                                                <i class="fas fa-file-contract item-icon"></i>
                                                <span>יפוי הכח המפורט בטופס 8832 הרשמי</span>
                                            </div>
                                            <div class="agreement-item-unified">
                                                <i class="fas fa-shield-alt item-icon"></i>
                                                <span>עיבוד הנתונים האישיים לצורך חישוב המס</span>
                                            </div>
                                            <div class="agreement-item-unified">
                                                <i class="fas fa-user-check item-icon"></i>
                                                <span>נכונות ומדויקות כל הפרטים שמסרתי</span>
                                            </div>
                                            <div class="agreement-item-unified">
                                                <i class="fas fa-percentage item-icon"></i>
                                                <span>תשלום עמלת הצלחה של 15% במקרה של החזר מס</span>
                                            </div>
                                        </div>
                                        <div class="agreement-legal-note">
                                            <i class="fas fa-gavel"></i>
                                            <span>החתימה הדיגיטלית והאישור מהווים הסכם משפטי מחייב</span>
                                        </div>
                                    </div>
                                </label>
                            </div>

                            <div class="agreement-note">
                                <div class="note-icon">
                                    <i class="fas fa-info-circle"></i>
                                </div>
                                <div class="note-text">
                                    <strong>שים לב:</strong> החתימה הדיגיטלית הרשמית תתבצע בטופס 8832 למטה.
                                    סימון כל התיבות לעיל נדרש להמשך התהליך.
                                </div>
                            </div>

                            <div class="legal-explanation">
                                <div class="legal-title">⚖️ משמעות האישור</div>
                                <div class="legal-content">
                                    <p><strong>בהתאם לחוק החתימה האלקטרונית התשס"א-2001:</strong></p>
                                    <ul>
                                        <li>סימון התיבות מהווה אישור מלא לכל התנאים</li>
                                        <li>החתימה הרשמית בטופס 8832 מעניקה ייפוי כח ל-TaxFree Pro</li>
                                        <li>כל הנתונים נשמרים במערכת מאובטחת עם חותמת זמן</li>
                                        <li>ניתן לבטל את ההסכם תוך 14 ימים בהתאם לחוק הגנת הצרכן</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Final Action Buttons -->
                        <div class="final-actions">
                            <div class="action-summary">
                                <div class="summary-icon">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="summary-text">
                                    <h4>מוכן להמשך?</h4>
                                    <p>לאחר החתימה תועבר לחישוב החזר המס המקצועי</p>
                                </div>
                            </div>

                            <div class="action-buttons-clean">
                                <button type="button" id="proceedToCalculator" class="proceed-btn-clean" disabled>
                                    <div class="btn-icon-clean">
                                        <i class="fas fa-calculator"></i>
                                    </div>
                                    <div class="btn-content-clean">
                                        <div class="btn-title-clean">המשך לחישוב מס</div>
                                        <div class="btn-subtitle-clean">נדרש אישור התנאים וחתימה דיגיטלית</div>
                                    </div>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tax Calculator Step (Hidden initially) -->
                <div id="taxCalculatorStep" class="step-container" style="display: none;">
                    <div class="step-header">
                        <div class="step-number">3</div>
                        <h3>🧮 חישוב החזר מס</h3>
                        <p>העלה מסמכים לזיהוי אוטומטי או מלא את הפרטים ידנית</p>
                        <button type="button" class="back-btn" onclick="goBackToTerms()">
                            ← חזור לתנאי השימוש
                        </button>
                    </div>

                <!-- OCR Document Upload Section -->
                <div class="ocr-section">
                    <h3>📄 העלאת מסמכים אוטומטית</h3>
                    <p>העלה את המסמכים שלך והמערכת תקרא אותם אוטומטיט באמצעות OCR</p>

                    <div class="upload-area" id="uploadArea">
                        <div class="upload-content">
                            <div class="upload-icon">📁</div>
                            <h4>גרור קבצים לכאן או לחץ לבחירה</h4>
                            <p>תומך ב: PDF, JPG, PNG, TIFF, BMP, GIF, WebP, HEIC, DOC, DOCX, XLS, XLSX</p>
                            <input type="file" id="fileInput" multiple accept=".pdf,.jpg,.jpeg,.png,.tiff,.tif,.bmp,.gif,.webp,.heic,.heif,.doc,.docx,.xls,.xlsx" style="display: none;">
                            <button type="button" class="upload-btn" onclick="document.getElementById('fileInput').click()">
                                📤 בחר קבצים
                            </button>
                        </div>
                    </div>

                    <div class="document-types">
                        <h4>סוגי מסמכים נתמכים:</h4>
                        <div class="doc-grid">
                            <div class="doc-item">📋 טופס 106 - ריכוז שכר</div>
                            <div class="doc-item">💰 אישור ניכוי מס במקור</div>
                            <div class="doc-item">🏥 אישור דמי אבטלה</div>
                            <div class="doc-item">⚕️ אישור דמי פגיעה בעבודה</div>
                            <div class="doc-item">🎖️ תגמולי מילואים</div>
                            <div class="doc-item">👶 דמי לידה</div>
                            <div class="doc-item">📊 טופס 161 - פרישה/פיטורין</div>
                            <div class="doc-item">📈 טופס 867 - ניירות ערך</div>
                            <div class="doc-item">🏦 אישור קופות גמל</div>
                            <div class="doc-item">🏘️ אישור תושבות בישוב מזכה</div>
                            <div class="doc-item">🎓 טופס 119 - זכאות לתואר</div>
                            <div class="doc-item">♿ טופס 127 - ילד נטול יכולת</div>
                            <div class="doc-item">💝 קבלות תרומות</div>
                        </div>
                    </div>

                    <div id="uploadedFiles" class="uploaded-files"></div>
                    <div id="ocrProgress" class="ocr-progress" style="display: none;">
                        <div class="progress-bar">
                            <div class="progress-fill"></div>
                        </div>
                        <p>מעבד מסמכים... <span id="progressText">0%</span></p>
                    </div>

                    <div class="ocr-results" id="ocrResults" style="display: none;">
                        <h4>📊 נתונים שנמצאו במסמכים:</h4>
                        <div id="extractedData"></div>
                        <button type="button" class="apply-data-btn" onclick="applyExtractedData()">
                            ✅ החל נתונים על הטופס
                        </button>
                    </div>
                </div>

                <div class="divider">
                    <span>או מלא ידנית</span>
                </div>
                
                    <form id="taxForm">
                    <div class="form-grid">
                        <!-- Basic Info -->
                        <div>
                            <h3 class="form-section-title">📋 פרטים בסיסיים</h3>
                            <div class="form-group">
                                <label for="year">שנת מס:</label>
                                <select id="year" required>
                                    <option value="">בחר שנה</option>
                                    <option value="2020">2020</option>
                                    <option value="2021">2021</option>
                                    <option value="2022">2022</option>
                                    <option value="2023">2023</option>
                                    <option value="2024">2024</option>
                                    <option value="2025">2025</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="gender">מין:</label>
                                <select id="gender" required>
                                    <option value="">בחר מין</option>
                                    <option value="male">גבר</option>
                                    <option value="female">אישה</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="income">הכנסה שנתית ברוטו (₪):</label>
                                <input type="number" id="income" placeholder="לדוגמה: 350000" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="taxPaid">מס ששולם בפועל (₪):</label>
                                <input type="number" id="taxPaid" placeholder="לדוגמה: 85000">
                            </div>
                        </div>

                        <!-- Deductions -->
                        <div>
                            <h3 class="form-section-title">💰 ניכויים</h3>
                            <div class="form-group">
                                <label for="donations">תרומות (₪):</label>
                                <input type="number" id="donations" placeholder="עד 35% מההכנסה">
                                <small style="color: #6b7280; font-size: 0.9em;">זיכוי 35% מסכום התרומה, עד 35% מההכנסה החייבת</small>
                            </div>
                            
                            <div class="form-group">
                                <label for="studyFund">קרן השתלמות (₪):</label>
                                <input type="number" id="studyFund" placeholder="תקרה 2025: 21,096 ₪">
                                <small style="color: #6b7280; font-size: 0.9em;">זיכוי 35% עד תקרה של 21,096 ₪</small>
                            </div>
                            
                            <div class="form-group">
                                <label for="foreignTax">מס ששולם בחו"ל (₪):</label>
                                <input type="number" id="foreignTax" placeholder="לזיכוי מס זר">
                            </div>
                        </div>
                    </div>

                    <!-- פטורים והטבות מיוחדות -->
                    <div class="form-section">
                        <h3 style="color: #7c3aed; margin-bottom: 20px;">🎁 פטורים והטבות מיוחדות</h3>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="rentalIncome">הכנסה מהשכרת דירת מגורים (₪):</label>
                                <input type="number" id="rentalIncome" placeholder="פטור מלא עד 5,196 ₪">
                                <small style="color: #6b7280; font-size: 0.9em;">פטור מלא עד 5,196 ₪ + 3 מסלולי מיסוי מועדפים (כולל מס 10% קבוע או מדרגות לפי גיל)</small>
                            </div>

                            <div class="form-group">
                                <label for="scholarships">מלגות לימוד ומחקר (₪):</label>
                                <input type="number" id="scholarships" placeholder="פטור עד 98,000 ₪">
                                <small style="color: #6b7280; font-size: 0.9em;">פטור עד 98,000 ₪ לשנה</small>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="agricultureIncome">הכנסה מחקלאות (₪):</label>
                                <input type="number" id="agricultureIncome" placeholder="פטור עד 70,800 ₪">
                                <small style="color: #6b7280; font-size: 0.9em;">פטור עד 70,800 ₪ ביישובי זכאות לאומית</small>
                            </div>

                            <div class="form-group">
                                <label for="angelInvestment">השקעה לפי חוק האנג'ל (₪):</label>
                                <input type="number" id="angelInvestment" placeholder="זיכוי 25% עד 5M ₪">
                                <small style="color: #6b7280; font-size: 0.9em;">זיכוי 25% מההשקעה עד 5 מיליון ₪</small>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="artistIncome">הכנסה כאמן/יוצר (₪):</label>
                                <input type="number" id="artistIncome" placeholder="פטור 50% עד 300K ₪">
                                <small style="color: #6b7280; font-size: 0.9em;">פטור 50% מההכנסה עד 300,000 ₪</small>
                            </div>

                            <div class="form-group">
                                <label for="disability">אחוז נכות רפואית:</label>
                                <select id="disability">
                                    <option value="0">ללא נכות</option>
                                    <option value="50">50% נכות</option>
                                    <option value="75">75% נכות</option>
                                    <option value="90">90% נכות ומעלה</option>
                                    <option value="100">100% נכות</option>
                                </select>
                                <small style="color: #6b7280; font-size: 0.9em;">נכות 90%+ זכאית לפטורים ונקודות זיכוי נוספות</small>
                            </div>
                        </div>

                        <!-- תיבות סימון להטבות -->
                        <div class="checkbox-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; margin-top: 20px;">
                            <label class="checkbox-container">
                                <input type="checkbox" id="isQualifiedSettlement">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">מתגורר ביישוב זכאות לאומית</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="isArtist">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">אמן/יוצר מוכר</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="isSecurityZone">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">תושב אזור ביטחון (עוטף עזה)</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="isNewImmigrant">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">עולה חדש</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="isReturningResident">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">תושב חוזר</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="isDischargedSoldier">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">חייל משוחרר (עד שנתיים)</span>
                            </label>
                        </div>

                        <!-- הכנסות נוספות ופטורים -->
                        <div class="form-row">
                            <div class="form-group">
                                <label for="socialSecurityBenefits">קצבאות ביטוח לאומי (₪):</label>
                                <input type="number" id="socialSecurityBenefits" placeholder="זקנה, נכות, שארים, ילדים">
                                <small style="color: #6b7280; font-size: 0.9em;">פטור מלא (למעט קצבת פרישה)</small>
                            </div>

                            <div class="form-group">
                                <label for="pensionBenefits">פנסיות ותגמולים (₪):</label>
                                <input type="number" id="pensionBenefits" placeholder="פנסיה תקציבית, פיצויים">
                                <small style="color: #6b7280; font-size: 0.9em;">פטור על פנסיה תקציבית ופיצויים</small>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="capitalLosses">הפסדים בשוק ההון (₪):</label>
                                <input type="number" id="capitalLosses" placeholder="הפסדי רווח הון">
                                <small style="color: #6b7280; font-size: 0.9em;">קיזוז מול רווחי הון למשך 5 שנים</small>
                            </div>

                            <div class="form-group">
                                <label for="workInjuryCompensation">פיצויי נפגעי עבודה/תאונה (₪):</label>
                                <input type="number" id="workInjuryCompensation" placeholder="פיצויים לנפגעי עבודה">
                                <small style="color: #6b7280; font-size: 0.9em;">פטור מלא ממס</small>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="reserveDutyPay">דמי מילואים (₪):</label>
                                <input type="number" id="reserveDutyPay" placeholder="דמי מילואים">
                                <small style="color: #6b7280; font-size: 0.9em;">פטור מלא על דמי מילואים</small>
                            </div>

                            <div class="form-group">
                                <label for="employeeOptions">הכנסה מאופציות עובדים (₪):</label>
                                <input type="number" id="employeeOptions" placeholder="מימוש אופציות">
                                <small style="color: #6b7280; font-size: 0.9em;">מסלול רווח הון 25% (במקום עד 50%)</small>
                            </div>
                        </div>

                        <!-- פטורים ממסים על נדל"ן -->
                        <div class="form-section">
                            <h4 style="color: #7c3aed; margin: 20px 0 15px 0;">🏠 פטורים ממסים על נדל"ן</h4>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="propertyPurchaseValue">שווי דירה לרכישה (₪):</label>
                                    <input type="number" id="propertyPurchaseValue" placeholder="שווי הדירה">
                                    <small style="color: #6b7280; font-size: 0.9em;">פטור מלא עד 1.98M ₪ לרוכש ראשון</small>
                                </div>

                                <div class="form-group">
                                    <label for="propertySaleValue">שווי דירה למכירה (₪):</label>
                                    <input type="number" id="propertySaleValue" placeholder="שווי דירה נמכרת">
                                    <small style="color: #6b7280; font-size: 0.9em;">פטור ממס שבח לדירה יחידה</small>
                                </div>
                            </div>

                            <div class="checkbox-grid" style="margin-top: 15px;">
                                <label class="checkbox-container">
                                    <input type="checkbox" id="isFirstHomeBuyer">
                                    <span class="checkmark"></span>
                                    <span class="checkbox-text">רוכש דירה ראשון</span>
                                </label>

                                <label class="checkbox-container">
                                    <input type="checkbox" id="isHousingUpgrader">
                                    <span class="checkmark"></span>
                                    <span class="checkbox-text">משפר דיור</span>
                                </label>

                                <label class="checkbox-container">
                                    <input type="checkbox" id="isInheritanceProperty">
                                    <span class="checkmark"></span>
                                    <span class="checkbox-text">דירת ירושה</span>
                                </label>

                                <label class="checkbox-container">
                                    <input type="checkbox" id="isGiftProperty">
                                    <span class="checkmark"></span>
                                    <span class="checkbox-text">מתנה בין קרובי משפחה</span>
                                </label>
                            </div>
                        </div>

                        <!-- פטורים עסקיים ומע"מ -->
                        <div class="form-section">
                            <h4 style="color: #7c3aed; margin: 20px 0 15px 0;">💼 פטורים עסקיים ומע"מ</h4>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="businessTurnover">מחזור עסקי שנתי (₪):</label>
                                    <input type="number" id="businessTurnover" placeholder="מחזור שנתי">
                                    <small style="color: #6b7280; font-size: 0.9em;">פטור ממע"מ עד 102,000 ₪</small>
                                </div>

                                <div class="form-group">
                                    <label for="personalImportValue">יבוא אישי (USD):</label>
                                    <input type="number" id="personalImportValue" placeholder="שווי יבוא אישי">
                                    <small style="color: #6b7280; font-size: 0.9em;">פטור ממכס ומע"מ עד 75$</small>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="pensionFundDeposits">הפקדות קרן פנסיה (₪):</label>
                                    <input type="number" id="pensionFundDeposits" placeholder="הפקדות שנתיות">
                                    <small style="color: #6b7280; font-size: 0.9em;">פטור עד 18,960 ₪ (עצמאים) / 18,854 ₪ (שכירים)</small>
                                </div>

                                <div class="form-group">
                                    <label for="providentFundDeposits">הפקדות קופת גמל (₪):</label>
                                    <input type="number" id="providentFundDeposits" placeholder="הפקדות קופת גמל">
                                    <small style="color: #6b7280; font-size: 0.9em;">פטור עד תקרה של 70,000 ₪ בשנה</small>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="managerInsurance">ביטוח מנהלים (₪):</label>
                                    <input type="number" id="managerInsurance" placeholder="דמי ביטוח מנהלים">
                                    <small style="color: #6b7280; font-size: 0.9em;">פטור עד 16% או 300,000 ₪</small>
                                </div>

                                <div class="form-group">
                                    <label for="companyLiquidationBenefits">הטבות פירוק חברה (₪):</label>
                                    <input type="number" id="companyLiquidationBenefits" placeholder="רווחים מפירוק">
                                    <small style="color: #6b7280; font-size: 0.9em;">מס דיבידנד מופחת 10% (2025)</small>
                                </div>
                            </div>
                        </div>

                        <!-- פטורים נוספים שחסרו מהטבלה המקורית -->
                        <div class="form-section">
                            <h4 style="color: #7c3aed; margin: 20px 0 15px 0;">📋 פטורים נוספים מהטבלה</h4>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="studyFundWithdrawal">משיכה מקרן השתלמות (₪):</label>
                                    <input type="number" id="studyFundWithdrawal" placeholder="משיכה אחרי 6 שנים">
                                    <small style="color: #6b7280; font-size: 0.9em;">פטור מלא במשיכה אחרי 6 שנים</small>
                                </div>

                                <div class="form-group">
                                    <label for="peripherySettlementBonus">תוספת נקודות זיכוי פריפריה:</label>
                                    <select id="peripherySettlementBonus">
                                        <option value="0">לא תושב פריפריה</option>
                                        <option value="0.5">0.5 נקודות זיכוי</option>
                                        <option value="1">1 נקודת זיכוי</option>
                                        <option value="1.5">1.5 נקודות זיכוי</option>
                                        <option value="2">2 נקודות זיכוי</option>
                                    </select>
                                    <small style="color: #6b7280; font-size: 0.9em;">תוספת נקודות זיכוי לישובים מוכרים</small>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="foreignDividends">דיבידנדים מחו"ל (₪):</label>
                                    <input type="number" id="foreignDividends" placeholder="דיבידנדים מחו״ל">
                                    <small style="color: #6b7280; font-size: 0.9em;">טיפול לפי אמנות מס (גרמניה - פטור מלא)</small>
                                </div>

                                <div class="form-group">
                                    <label for="foreignRoyalties">תמלוגים מחו"ל (₪):</label>
                                    <input type="number" id="foreignRoyalties" placeholder="תמלוגים מחו״ל">
                                    <small style="color: #6b7280; font-size: 0.9em;">חייב במס עם זיכוי על מס ששולם בחו"ל</small>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="foreignInterest">ריבית מחו"ל (₪):</label>
                                    <input type="number" id="foreignInterest" placeholder="ריבית מחו״ל">
                                    <small style="color: #6b7280; font-size: 0.9em;">חייב במס עם זיכוי על מס ששולם בחו"ל</small>
                                </div>

                                <div class="form-group">
                                    <label for="developmentAreaInvestmentAmount">השקעה באזור תע"ן (₪):</label>
                                    <input type="number" id="developmentAreaInvestmentAmount" placeholder="סכום השקעה">
                                    <small style="color: #6b7280; font-size: 0.9em;">זיכוי השקעה 30% מסכום ההשקעה</small>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="studentsOver18">ילדים סטודנטים מעל 18 (מספר):</label>
                                    <input type="number" id="studentsOver18" placeholder="מספר ילדים סטודנטים">
                                    <small style="color: #6b7280; font-size: 0.9em;">2 נקודות זיכוי לכל ילד סטודנט מעל 18</small>
                                </div>

                                <div class="form-group">
                                    <label for="doctorSpecialCredit">זיכוי מיוחד לרופאים (₪):</label>
                                    <input type="number" id="doctorSpecialCredit" placeholder="עד 18,000 ₪">
                                    <small style="color: #6b7280; font-size: 0.9em;">זיכוי שנתי עד 18,000 ₪ לרופאים במערכת ציבורית</small>
                                </div>
                            </div>

                            <!-- פטורים נוספים שחסרו מהטבלה המקורית -->
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="militaryServiceType">סוג שירות צבאי:</label>
                                    <select id="militaryServiceType">
                                        <option value="none">ללא שירות צבאי</option>
                                        <option value="full">שירות צבאי מלא</option>
                                        <option value="partial">שירות צבאי חלקי</option>
                                        <option value="combat">שירות קרבי</option>
                                        <option value="officer">קצין</option>
                                    </select>
                                    <small style="color: #6b7280; font-size: 0.9em;">זיכוי נקודות לפי סוג השירות הצבאי</small>
                                </div>

                                <div class="form-group">
                                    <label for="academicDegrees">תארים אקדמיים:</label>
                                    <select id="academicDegrees" multiple>
                                        <option value="bachelor">תואר ראשון</option>
                                        <option value="master">תואר שני</option>
                                        <option value="doctorate">תואר שלישי</option>
                                        <option value="professional">תואר מקצועי</option>
                                    </select>
                                    <small style="color: #6b7280; font-size: 0.9em;">זיכוי נקודות לכל תואר אקדמי</small>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="newImmigrantYears">שנות עלייה/חזרה:</label>
                                    <select id="newImmigrantYears">
                                        <option value="none">לא עולה חדש/תושב חוזר</option>
                                        <option value="0-18">0-18 חודשים</option>
                                        <option value="19-30">19-30 חודשים</option>
                                        <option value="31-42">31-42 חודשים</option>
                                        <option value="over-42">מעל 42 חודשים</option>
                                    </select>
                                    <small style="color: #6b7280; font-size: 0.9em;">זיכוי נקודות לפי תקופת העלייה/חזרה</small>
                                </div>

                                <div class="form-group">
                                    <label for="singleParentChildren">ילדים להורה יחיד (מספר):</label>
                                    <input type="number" id="singleParentChildren" placeholder="מספר ילדים">
                                    <small style="color: #6b7280; font-size: 0.9em;">זיכוי נוסף להורה יחיד</small>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="disabledChildren">ילדים עם נכות (מספר):</label>
                                    <input type="number" id="disabledChildren" placeholder="מספר ילדים עם נכות">
                                    <small style="color: #6b7280; font-size: 0.9em;">זיכוי נוסף לילדים עם נכות</small>
                                </div>

                                <div class="form-group">
                                    <label for="workingWomanBonus">בונוס אישה עובדת (₪):</label>
                                    <input type="number" id="workingWomanBonus" placeholder="בונוס שנתי">
                                    <small style="color: #6b7280; font-size: 0.9em;">בונוס נוסף לאישה עובדת מעבר לנקודות הזיכוי</small>
                                </div>
                            </div>

                            <!-- זיכויים בסיסיים שחסרו מהטבלה -->
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="childrenUnder6">ילדים עד גיל 6 (מספר):</label>
                                    <input type="number" id="childrenUnder6" placeholder="מספר ילדים עד 6">
                                    <small style="color: #6b7280; font-size: 0.9em;">3.5 נקודות זיכוי לכל ילד עד גיל 6</small>
                                </div>

                                <div class="form-group">
                                    <label for="children6to18">ילדים גיל 6-18 (מספר):</label>
                                    <input type="number" id="children6to18" placeholder="מספר ילדים 6-18">
                                    <small style="color: #6b7280; font-size: 0.9em;">2.5 נקודות זיכוי לכל ילד גיל 6-18</small>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="mortgageInterestRental">ריבית משכנתא דירה להשכרה (₪):</label>
                                    <input type="number" id="mortgageInterestRental" placeholder="ריבית שנתית">
                                    <small style="color: #6b7280; font-size: 0.9em;">ניכוי ריבית משכנתא גם לדירה להשכרה</small>
                                </div>

                                <div class="form-group">
                                    <label for="basicCreditPoints">נקודות זיכוי בסיסיות:</label>
                                    <input type="number" id="basicCreditPoints" value="2.25" readonly>
                                    <small style="color: #6b7280; font-size: 0.9em;">זיכוי בסיסי לכל אזרח (2.25 נקודות)</small>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="studyFundDeposits">הפקדות קרן השתלמות (₪):</label>
                                    <input type="number" id="studyFundDeposits" placeholder="הפקדות שנתיות">
                                    <small style="color: #6b7280; font-size: 0.9em;">זיכוי 35% מהסכום המופרש, תקרה 21,096 ₪</small>
                                </div>

                                <div class="form-group">
                                    <label for="donationsCarryforward">עודף תרומות משנים קודמות (₪):</label>
                                    <input type="number" id="donationsCarryforward" placeholder="עודף מ-3 שנים אחרונות">
                                    <small style="color: #6b7280; font-size: 0.9em;">ניתן להעביר עודף תרומות עד 3 שנים</small>
                                </div>
                            </div>
                        </div>

                        <!-- תיבות סימון נוספות -->
                        <div class="checkbox-grid" style="margin-top: 20px;">
                            <label class="checkbox-container">
                                <input type="checkbox" id="isSmallBusiness">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">עוסק פטור ממע"מ</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="isFinancialServices">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">שירותים פיננסיים</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="isReturningResidentVeteran">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">תושב חוזר ותיק (10+ שנים)</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="hasHighCapitalIncome">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">הכנסות הוניות מעל 721,560 ₪</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="isCompanyLiquidation2025">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">מפרק חברה ב-2025</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="hasEmployeeOptions">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">בעל אופציות עובדים</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="hasGermanTaxTreaty">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">הכנסות מגרמניה (אמנת מס)</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="isPeripheryResident">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">תושב יישוב פריפריה מוכר</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="hasStudyFundOver6Years">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">קרן השתלמות מעל 6 שנים</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="isPublicHealthDoctor">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">רופא במערכת בריאות ציבורית</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="hasDevelopmentAreaInvestment">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">השקעה באזור תע"ן</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="isWorkingWoman">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">אישה עובדת (1.5 נקודות זיכוי)</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="isSingleParent">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">הורה יחיד</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="hasSpouseNoIncome">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">בן/בת זוג ללא הכנסה</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="hasDisabledChildren">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">יש ילדים עם נכות</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="hasCombatService">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">שירות קרבי</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="isOfficer">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">קצין לשעבר</span>
                            </label>
                        </div>

                        <div class="info-box" style="background: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 8px; padding: 15px; margin-top: 20px;">
                            <h4 style="color: #0369a1; margin: 0 0 10px 0;">💡 הסבר על כל הפטורים וההטבות</h4>
                            <ul style="margin: 0; padding-right: 20px; color: #374151; font-size: 0.9em;">
                                <li><strong>אזור ביטחון:</strong> זיכוי 20% מההכנסה עד 246,120 ₪</li>
                                <li><strong>נכות 90%+:</strong> פטורים מדורגים + 2.25 נקודות זיכוי נוספות</li>
                                <li><strong>עולה חדש:</strong> פטור מלא על הכנסות מחו"ל</li>
                                <li><strong>תושב חוזר ותיק:</strong> פטור על כל ההכנסות מחו"ל (10+ שנים)</li>
                                <li><strong>חייל משוחרר:</strong> נקודת זיכוי אחת נוספת לשנתיים</li>
                                <li><strong>רוכש ראשון:</strong> פטור ממס רכישה עד 1.98M ₪</li>
                                <li><strong>אופציות עובדים:</strong> מסלול רווח הון 25% במקום מס שולי</li>
                                <li><strong>פירוק חברה 2025:</strong> מס דיבידנד מופחת 10%</li>
                                <li><strong>מס היסף נדל"ן:</strong> פטור על דירות עד 5.38M ₪</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Children -->
                    <div>
                        <h3 class="form-section-title">👶 ילדים</h3>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="children6to18">ילדים בגיל 6-18:</label>
                                <input type="number" id="children6to18" min="0" value="0">
                            </div>
                            <div class="form-group">
                                <label for="childrenUnder6">ילדים מתחת לגיל 6:</label>
                                <input type="number" id="childrenUnder6" min="0" value="0">
                            </div>
                        </div>
                    </div>

                    <!-- Rights and Benefits -->
                    <div>
                        <h3 class="form-section-title">🎯 זכויות והטבות</h3>
                        <div class="checkbox-grid">
                            <div class="checkbox-item">
                                <label>תואר ראשון</label>
                                <input type="checkbox" id="degree1">
                            </div>
                            <div class="checkbox-item">
                                <label>תואר שני</label>
                                <input type="checkbox" id="degree2">
                            </div>
                            <div class="checkbox-item">
                                <label>תואר שלישי</label>
                                <input type="checkbox" id="degree3">
                            </div>
                            <div class="checkbox-item">
                                <label>חייל משוחרר - שירות מלא</label>
                                <input type="checkbox" id="militaryFull">
                            </div>
                            <div class="checkbox-item">
                                <label>חייל משוחרר - שירות חלקי</label>
                                <input type="checkbox" id="militaryPartial">
                            </div>
                            <div class="checkbox-item">
                                <label>הורה יחידני</label>
                                <input type="checkbox" id="singleParent">
                            </div>
                            <div class="checkbox-item">
                                <label>בן/בת זוג ללא הכנסה</label>
                                <input type="checkbox" id="spouseNoIncome">
                            </div>
                            <div class="checkbox-item">
                                <label>עולה חדש (0-18 חודשים)</label>
                                <input type="checkbox" id="newImmigrant0to18">
                            </div>
                            <div class="checkbox-item">
                                <label>עולה חדש (19-30 חודשים)</label>
                                <input type="checkbox" id="newImmigrant19to30">
                            </div>
                            <div class="checkbox-item">
                                <label>עולה חדש (31-42 חודשים)</label>
                                <input type="checkbox" id="newImmigrant31to42">
                            </div>
                            <div class="checkbox-item">
                                <label>הורה לילד עם מוגבלות</label>
                                <input type="checkbox" id="disabledChild">
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="calculate-btn">🧮 חשב החזר מס</button>
                    </form>

                    <!-- Results -->
                    <div id="result" class="result-section">
                        <h3>📊 תוצאות החישוב</h3>
                        <div id="resultContent"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>



    <!-- PDF.js for PDF processing - Latest version -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/4.0.379/pdf.min.js"></script>

    <!-- HEIC2any for HEIC/HEIF conversion - Latest version -->
    <script src="https://cdn.jsdelivr.net/npm/heic2any@0.0.4/dist/heic2any.min.js"></script>

    <!-- Tesseract.js for OCR - Latest version with better Hebrew support -->
    <script src="https://unpkg.com/tesseract.js@5.0.4/dist/tesseract.min.js"></script>

    <!-- SheetJS for Excel processing -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

    <!-- Mammoth.js for Word processing -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mammoth/1.6.0/mammoth.browser.min.js"></script>

    <!-- Help Modal -->
    <div id="helpModal" class="modal" style="display: none;" aria-hidden="true" role="dialog" aria-labelledby="helpModalTitle">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="helpModalTitle">
                    <i class="fas fa-question-circle"></i>
                    עזרה ותמיכה
                </h2>
                <button class="close-btn" onclick="closeHelp()" aria-label="סגור">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="modal-body">
                <div class="help-tabs" role="tablist">
                    <button class="tab-btn active" onclick="showHelpTab('guide')" role="tab" aria-selected="true">
                        <i class="fas fa-book"></i>
                        מדריך שימוש
                    </button>
                    <button class="tab-btn" onclick="showHelpTab('faq')" role="tab" aria-selected="false">
                        <i class="fas fa-question"></i>
                        שאלות נפוצות
                    </button>
                    <button class="tab-btn" onclick="showHelpTab('contact')" role="tab" aria-selected="false">
                        <i class="fas fa-phone"></i>
                        צור קשר
                    </button>
                    <button class="tab-btn" onclick="showHelpTab('accessibility')" role="tab" aria-selected="false">
                        <i class="fas fa-universal-access"></i>
                        נגישות
                    </button>
                </div>

                <div id="guide-tab" class="tab-content active" role="tabpanel">
                    <h3>מדריך שימוש במערכת</h3>
                    <div class="help-steps">
                        <div class="help-step">
                            <div class="step-number">1</div>
                            <div class="step-content">
                                <h4>קריאת תנאי השימוש</h4>
                                <p>קרא בעיון את כל סעיפי תנאי השימוש. כל סעיף שנקרא יסומן כהושלם ופס ההתקדמות יתעדכן.</p>
                            </div>
                        </div>
                        <div class="help-step">
                            <div class="step-number">2</div>
                            <div class="step-content">
                                <h4>טופס 8832 - יפוי כח</h4>
                                <p>טופס זה מאפשר לנו לטפל בבקשת החזר המס שלך ברשות המסים. הטופס מאושר ורשמי.</p>
                            </div>
                        </div>
                        <div class="help-step">
                            <div class="step-number">3</div>
                            <div class="step-content">
                                <h4>חתימה דיגיטלית מאובטחת</h4>
                                <p>חתום על הטופס באמצעות החתימה הדיגיטלית המאובטחת. החתימה נשמרת עם חותמת זמן.</p>
                            </div>
                        </div>
                        <div class="help-step">
                            <div class="step-number">4</div>
                            <div class="step-content">
                                <h4>מעבר לחישוב מס</h4>
                                <p>לאחר השלמת כל הסעיפים והחתימה, תוכל להמשיך לחישוב החזר המס המקצועי.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="faq-tab" class="tab-content" role="tabpanel">
                    <h3>שאלות נפוצות</h3>
                    <div class="faq-list">
                        <div class="faq-item">
                            <button class="faq-question" onclick="toggleFaq(this)" aria-expanded="false">
                                <span>מה זה טופס 8832?</span>
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            <div class="faq-answer">
                                <p>טופס 8832 הוא יפוי כח רשמי לרשות המסים המאפשר לנו לטפל בבקשת החזר המס שלך. זהו טופס סטנדרטי ומאושר על ידי רשות המסים הישראלית.</p>
                            </div>
                        </div>
                        <div class="faq-item">
                            <button class="faq-question" onclick="toggleFaq(this)" aria-expanded="false">
                                <span>האם החתימה הדיגיטלית בטוחה?</span>
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            <div class="faq-answer">
                                <p>כן, החתימה הדיגיטלית מוצפנת ומאובטחת בהתאם לתקנים הבינלאומיים ולחוק החתימה האלקטרונית הישראלי. כל חתימה נשמרת עם חותמת זמן ומידע מזהה.</p>
                            </div>
                        </div>
                        <div class="faq-item">
                            <button class="faq-question" onclick="toggleFaq(this)" aria-expanded="false">
                                <span>כמה זמן לוקח התהליך?</span>
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            <div class="faq-answer">
                                <p>קריאת תנאי השימוש וחתימה על הטופס אורכים כ-10-15 דקות. חישוב החזר המס נוסף כ-5-10 דקות. התהליך כולו פשוט ומהיר.</p>
                            </div>
                        </div>
                        <div class="faq-item">
                            <button class="faq-question" onclick="toggleFaq(this)" aria-expanded="false">
                                <span>מה קורה אחרי החתימה?</span>
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            <div class="faq-answer">
                                <p>לאחר החתימה תועבר לשלב חישוב החזר המס, שם תוכל להזין את הנתונים הפיננסיים שלך ולקבל חישוב מדויק של החזר המס הצפוי.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="contact-tab" class="tab-content" role="tabpanel">
                    <h3>צור קשר</h3>
                    <div class="contact-info">
                        <div class="contact-item">
                            <i class="fas fa-phone"></i>
                            <div>
                                <strong>טלפון:</strong>
                                <p>03-1234567 (ימים א'-ה', 9:00-18:00)</p>
                            </div>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-envelope"></i>
                            <div>
                                <strong>אימייל:</strong>
                                <p><EMAIL></p>
                            </div>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-comments"></i>
                            <div>
                                <strong>צ'אט חי:</strong>
                                <p>זמין 24/7 באתר</p>
                            </div>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <div>
                                <strong>כתובת:</strong>
                                <p>רחוב הרצל 123, תל אביב-יפו</p>
                            </div>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-clock"></i>
                            <div>
                                <strong>שעות פעילות:</strong>
                                <p>א'-ה': 9:00-18:00<br>ו': 9:00-13:00<br>ש': סגור</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="accessibility-tab" class="tab-content" role="tabpanel">
                    <h3>תכונות נגישות</h3>
                    <div class="accessibility-features">
                        <div class="feature-item">
                            <i class="fas fa-moon"></i>
                            <div>
                                <strong>מצב לילה:</strong>
                                <p>החלף לצבעים כהים לקריאה נוחה יותר בתאורה חלשה</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-text-height"></i>
                            <div>
                                <strong>גופן גדול:</strong>
                                <p>הגדל את גודל הטקסט לקריאה טובה יותר</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-adjust"></i>
                            <div>
                                <strong>ניגודיות גבוהה:</strong>
                                <p>הגבר את הניגודיות לראייה טובה יותר</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-book-open"></i>
                            <div>
                                <strong>מצב קריאה:</strong>
                                <p>מצב מיוחד לקריאה ממושכת ונוחה עם רקע בהיר</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-keyboard"></i>
                            <div>
                                <strong>קיצורי מקלדת:</strong>
                                <p>Alt+H - פתח עזרה | Alt+T - החלף נושא | ESC - סגור חלונות</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button class="btn-secondary" onclick="closeHelp()">
                    <i class="fas fa-times"></i>
                    סגור
                </button>
                <button class="btn-primary" onclick="startLiveChat()">
                    <i class="fas fa-comments"></i>
                    צ'אט חי
                </button>
            </div>
        </div>
    </div>

    <script src="calculator.js"></script>
    <script src="calculator_updated.js"></script>

    <script>
        // Load personal data from previous step
        function loadPersonalData() {
            const savedData = localStorage.getItem('taxfree_personal_data');
            if (savedData) {
                try {
                    const data = JSON.parse(savedData);
                    console.log('Loading personal data:', data);

                    // Update client information in the form if elements exist
                    const clientFullName = document.getElementById('clientFullName');
                    const clientIdNumber = document.getElementById('clientIdNumber');
                    const clientPhone = document.getElementById('clientPhone');
                    const clientEmail = document.getElementById('clientEmail');

                    if (clientFullName) clientFullName.textContent = data.fullName || '[שם לא זמין]';
                    if (clientIdNumber) clientIdNumber.textContent = data.idNumber || '[ת.ז לא זמינה]';
                    if (clientPhone) clientPhone.textContent = data.phone || '[טלפון לא זמין]';
                    if (clientEmail) clientEmail.textContent = data.email || '[אימייל לא זמין]';

                    // Update signature details if elements exist
                    const signerName = document.getElementById('signerName');
                    if (signerName) signerName.textContent = data.fullName || '-';

                    // Update power of attorney form if elements exist
                    const poaClientName = document.getElementById('poa-clientName');
                    const poaClientId = document.getElementById('poa-clientId');
                    const poaClientPhone = document.getElementById('poa-clientPhone');
                    const poaClientEmail = document.getElementById('poa-clientEmail');

                    if (poaClientName) poaClientName.textContent = data.fullName || '[שם לא זמין]';
                    if (poaClientId) poaClientId.textContent = data.idNumber || '[ת.ז לא זמינה]';
                    if (poaClientPhone) poaClientPhone.textContent = data.phone || '[טלפון לא זמין]';
                    if (poaClientEmail) poaClientEmail.textContent = data.email || '[אימייל לא זמין]';

                    // Show welcome message
                    showWelcomeMessage(data.fullName);

                } catch (error) {
                    console.error('Error loading personal data:', error);
                }
            } else {
                console.log('No personal data found in localStorage');
                showNoDataMessage();
            }
        }

        function showWelcomeMessage(name) {
            // Create and show a welcome message
            const welcomeDiv = document.createElement('div');
            welcomeDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #28a745, #20c997);
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 1000;
                font-weight: bold;
                animation: slideIn 0.5s ease-out;
            `;
            welcomeDiv.innerHTML = `
                <i class="fas fa-user-check" style="margin-left: 8px;"></i>
                שלום ${name}! ברוך הבא לעמוד תנאי השימוש
            `;

            document.body.appendChild(welcomeDiv);

            // Remove after 5 seconds
            setTimeout(() => {
                welcomeDiv.remove();
            }, 5000);
        }

        function showNoDataMessage() {
            // Show message if no data found
            const messageDiv = document.createElement('div');
            messageDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #ffc107, #fd7e14);
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 1000;
                font-weight: bold;
            `;
            messageDiv.innerHTML = `
                <i class="fas fa-exclamation-triangle" style="margin-left: 8px;"></i>
                לא נמצאו פרטים אישיים. אנא מלא תחילה את הפרטים האישיים.
                <br>
                <button onclick="window.location.href='taxfree_pro_complete_website.html'"
                        style="background: white; color: #ffc107; border: none; padding: 5px 10px; border-radius: 5px; margin-top: 10px; cursor: pointer;">
                    חזור לפרטים אישיים
                </button>
            `;

            document.body.appendChild(messageDiv);
        }

        // Add CSS animation for welcome message
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
        `;
        document.head.appendChild(style);

        // Function to go back to personal details page
        function goBackToTerms() {
            if (confirm('האם ברצונך לחזור לעמוד הפרטים האישיים?\n\nהנתונים שמולאו כאן יישמרו.')) {
                window.location.href = 'taxfree_pro_complete_website.html';
            }
        }

        // Progress tracking for terms
        let completedSections = new Set();
        const totalSections = 6; // Total number of terms sections

        function updateTermsProgress() {
            const progressFill = document.getElementById('termsProgress');
            const progressText = document.getElementById('progressText');

            if (progressFill && progressText) {
                const percentage = (completedSections.size / totalSections) * 100;
                progressFill.style.width = percentage + '%';
                progressText.textContent = `התקדמות: ${completedSections.size} מתוך ${totalSections} סעיפים הושלמו`;

                // Enable proceed button if all sections completed
                const proceedBtn = document.getElementById('proceedToCalculator');
                if (proceedBtn) {
                    if (completedSections.size === totalSections) {
                        proceedBtn.disabled = false;
                        proceedBtn.innerHTML = `
                            <i class="fas fa-calculator"></i>
                            המשך לחישוב מס
                            <span class="btn-subtitle">כל הסעיפים הושלמו ✓</span>
                        `;
                    } else {
                        proceedBtn.disabled = true;
                        proceedBtn.innerHTML = `
                            <i class="fas fa-calculator"></i>
                            המשך לחישוב מס
                            <span class="btn-subtitle">נותרו ${totalSections - completedSections.size} סעיפים</span>
                        `;
                    }
                }
            }
        }

        // Mark section as completed
        function markSectionCompleted(sectionId) {
            completedSections.add(sectionId);
            updateTermsProgress();

            // Add visual feedback
            const section = document.getElementById(sectionId);
            if (section) {
                section.style.borderLeft = '6px solid #28a745';
                section.style.background = 'linear-gradient(135deg, #f8fff8 0%, #ffffff 100%)';
            }
        }

        // Enhanced client welcome update
        function updateClientWelcome(data) {
            const clientWelcome = document.getElementById('clientWelcome');
            if (clientWelcome && data) {
                clientWelcome.innerHTML = `
                    <i class="fas fa-user-circle"></i>
                    <span>שלום ${data.fullName} | ת.ז: ${data.idNumber}</span>
                `;
                clientWelcome.style.background = 'linear-gradient(135deg, #e8f5e8, #f0f9ff)';
                clientWelcome.style.border = '2px solid #28a745';
            }
        }

        // Enhanced welcome message
        function showWelcomeMessage(name) {
            const welcomeDiv = document.createElement('div');
            welcomeDiv.style.cssText = `
                position: fixed;
                top: 80px;
                right: 20px;
                background: linear-gradient(135deg, #28a745, #20c997);
                color: white;
                padding: 20px 25px;
                border-radius: 15px;
                box-shadow: 0 8px 25px rgba(0,0,0,0.15);
                z-index: 1001;
                font-weight: bold;
                animation: slideInBounce 0.8s ease-out;
                max-width: 350px;
            `;
            welcomeDiv.innerHTML = `
                <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 8px;">
                    <i class="fas fa-user-check" style="font-size: 1.2em;"></i>
                    <span style="font-size: 1.1em;">ברוך הבא ${name}!</span>
                </div>
                <div style="font-size: 0.9em; opacity: 0.9;">
                    עמוד תנאי השימוש וטופס 8832 נטען בהצלחה
                </div>
                <div style="width: 100%; height: 3px; background: rgba(255,255,255,0.3); border-radius: 2px; margin-top: 10px; overflow: hidden;">
                    <div style="width: 0%; height: 100%; background: white; border-radius: 2px; animation: progressLoad 5s ease-out;"></div>
                </div>
            `;

            document.body.appendChild(welcomeDiv);

            // Remove after 6 seconds
            setTimeout(() => {
                welcomeDiv.style.animation = 'slideOutBounce 0.5s ease-in';
                setTimeout(() => welcomeDiv.remove(), 500);
            }, 6000);
        }

        // Add enhanced animations
        const enhancedStyle = document.createElement('style');
        enhancedStyle.textContent = `
            @keyframes slideInBounce {
                0% {
                    transform: translateX(100%) scale(0.8);
                    opacity: 0;
                }
                60% {
                    transform: translateX(-10px) scale(1.05);
                    opacity: 1;
                }
                100% {
                    transform: translateX(0) scale(1);
                    opacity: 1;
                }
            }

            @keyframes slideOutBounce {
                0% {
                    transform: translateX(0) scale(1);
                    opacity: 1;
                }
                100% {
                    transform: translateX(100%) scale(0.8);
                    opacity: 0;
                }
            }

            @keyframes progressLoad {
                0% { width: 0%; }
                100% { width: 100%; }
            }
        `;
        document.head.appendChild(enhancedStyle);

        // Enhanced load personal data function
        function loadPersonalData() {
            const savedData = localStorage.getItem('taxfree_personal_data');
            if (savedData) {
                try {
                    const data = JSON.parse(savedData);
                    console.log('Loading personal data:', data);

                    // Update all client information
                    updateClientWelcome(data);

                    // Update client information in the form if elements exist
                    const clientFullName = document.getElementById('clientFullName');
                    const clientIdNumber = document.getElementById('clientIdNumber');
                    const clientPhone = document.getElementById('clientPhone');
                    const clientEmail = document.getElementById('clientEmail');

                    if (clientFullName) clientFullName.textContent = data.fullName || '[שם לא זמין]';
                    if (clientIdNumber) clientIdNumber.textContent = data.idNumber || '[ת.ז לא זמינה]';
                    if (clientPhone) clientPhone.textContent = data.phone || '[טלפון לא זמין]';
                    if (clientEmail) clientEmail.textContent = data.email || '[אימייל לא זמין]';

                    // Update signature details if elements exist
                    const signerName = document.getElementById('signerName');
                    if (signerName) signerName.textContent = data.fullName || '-';

                    // Update power of attorney form if elements exist
                    const poaClientName = document.getElementById('poa-clientName');
                    const poaClientId = document.getElementById('poa-clientId');
                    const poaClientPhone = document.getElementById('poa-clientPhone');
                    const poaClientEmail = document.getElementById('poa-clientEmail');

                    if (poaClientName) poaClientName.textContent = data.fullName || '[שם לא זמין]';
                    if (poaClientId) poaClientId.textContent = data.idNumber || '[ת.ז לא זמינה]';
                    if (poaClientPhone) poaClientPhone.textContent = data.phone || '[טלפון לא זמין]';
                    if (poaClientEmail) poaClientEmail.textContent = data.email || '[אימייל לא זמין]';

                    // Show enhanced welcome message
                    showWelcomeMessage(data.fullName);

                } catch (error) {
                    console.error('Error loading personal data:', error);
                }
            } else {
                console.log('No personal data found in localStorage');
                showNoDataMessage();
            }
        }

        // Load data when page loads
        document.addEventListener('DOMContentLoaded', function() {
            loadPersonalData();
            updateTermsProgress();

            // Add scroll spy for sections
            const sections = document.querySelectorAll('.terms-section');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.transform = 'translateY(0)';
                        entry.target.style.opacity = '1';
                    }
                });
            }, { threshold: 0.1 });

            sections.forEach(section => {
                section.style.transform = 'translateY(20px)';
                section.style.opacity = '0.8';
                section.style.transition = 'all 0.6s ease';
                observer.observe(section);
            });
        });

        // Enhanced Accessibility Functions
        function toggleTheme() {
            const body = document.body;
            const currentTheme = body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            body.setAttribute('data-theme', newTheme);

            const themeText = document.querySelector('.theme-text');
            if (themeText) {
                themeText.textContent = newTheme === 'dark' ? 'מצב יום' : 'מצב לילה';
            }

            localStorage.setItem('preferred-theme', newTheme);
        }

        function toggleFontSize() {
            const body = document.body;
            const currentSize = body.getAttribute('data-font-size');
            const newSize = currentSize === 'large' ? 'normal' : 'large';
            body.setAttribute('data-font-size', newSize);

            const fontText = document.querySelector('.font-text');
            if (fontText) {
                fontText.textContent = newSize === 'large' ? 'גופן רגיל' : 'גופן גדול';
            }

            localStorage.setItem('preferred-font-size', newSize);
        }

        function toggleHighContrast() {
            const body = document.body;
            const currentContrast = body.getAttribute('data-contrast');
            const newContrast = currentContrast === 'high' ? 'normal' : 'high';
            body.setAttribute('data-contrast', newContrast);

            const contrastText = document.querySelector('.contrast-text');
            if (contrastText) {
                contrastText.textContent = newContrast === 'high' ? 'ניגודיות רגילה' : 'ניגודיות גבוהה';
            }

            localStorage.setItem('preferred-contrast', newContrast);
        }

        function toggleReadingMode() {
            const body = document.body;
            const currentMode = body.getAttribute('data-reading-mode');
            const newMode = currentMode === 'true' ? 'false' : 'true';
            body.setAttribute('data-reading-mode', newMode);

            const readingText = document.querySelector('.reading-text');
            if (readingText) {
                readingText.textContent = newMode === 'true' ? 'מצב רגיל' : 'מצב קריאה';
            }

            localStorage.setItem('preferred-reading-mode', newMode);
        }

        function showHelp() {
            const modal = document.getElementById('helpModal');
            if (modal) {
                modal.style.display = 'flex';
                modal.setAttribute('aria-hidden', 'false');
                document.body.style.overflow = 'hidden';

                // Focus on first interactive element
                const firstButton = modal.querySelector('button');
                if (firstButton) firstButton.focus();
            }
        }

        function closeHelp() {
            const modal = document.getElementById('helpModal');
            if (modal) {
                modal.style.display = 'none';
                modal.setAttribute('aria-hidden', 'true');
                document.body.style.overflow = 'auto';
            }
        }

        function showHelpTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // Show selected tab
            const selectedTab = document.getElementById(tabName + '-tab');
            const selectedBtn = event.target.closest('.tab-btn');

            if (selectedTab) selectedTab.classList.add('active');
            if (selectedBtn) selectedBtn.classList.add('active');
        }

        function toggleFaq(button) {
            const faqItem = button.closest('.faq-item');
            const answer = faqItem.querySelector('.faq-answer');
            const icon = button.querySelector('i');

            const isOpen = answer.style.display === 'block';

            // Close all other FAQs
            document.querySelectorAll('.faq-answer').forEach(ans => {
                ans.style.display = 'none';
            });
            document.querySelectorAll('.faq-question i').forEach(ic => {
                ic.style.transform = 'rotate(0deg)';
            });

            // Toggle current FAQ
            if (!isOpen) {
                answer.style.display = 'block';
                icon.style.transform = 'rotate(180deg)';
            }
        }

        function startLiveChat() {
            // Simulate live chat opening
            alert('צ\'אט חי יפתח בקרוב! בינתיים ניתן ליצור קשר בטלפון: 03-1234567');
        }

        // Load user preferences on page load
        function loadUserPreferences() {
            const theme = localStorage.getItem('preferred-theme');
            const fontSize = localStorage.getItem('preferred-font-size');
            const contrast = localStorage.getItem('preferred-contrast');
            const readingMode = localStorage.getItem('preferred-reading-mode');

            if (theme) document.body.setAttribute('data-theme', theme);
            if (fontSize) document.body.setAttribute('data-font-size', fontSize);
            if (contrast) document.body.setAttribute('data-contrast', contrast);
            if (readingMode) document.body.setAttribute('data-reading-mode', readingMode);
        }

        // Enhanced keyboard navigation
        document.addEventListener('keydown', function(e) {
            // ESC to close modal
            if (e.key === 'Escape') {
                closeHelp();
            }

            // Alt + H to open help
            if (e.altKey && e.key === 'h') {
                e.preventDefault();
                showHelp();
            }

            // Alt + T to toggle theme
            if (e.altKey && e.key === 't') {
                e.preventDefault();
                toggleTheme();
            }
        });

        // SUPER SIMPLE SIGNATURE - INLINE FUNCTIONS
        let signCanvas = null;
        let signCtx = null;
        let signing = false;

        // FIXED SIGNATURE FUNCTIONS - SIMPLE AND WORKING
        function startSign(e) {
            console.log('🖊️ STARTING SIGNATURE!');

            // Get canvas if not exists
            if (!signCanvas) {
                signCanvas = document.getElementById('digitalSignatureCanvas');
                if (!signCanvas) {
                    console.error('❌ Canvas not found!');
                    return;
                }
                signCtx = signCanvas.getContext('2d');

                // Simple drawing settings
                signCtx.strokeStyle = '#1e293b';
                signCtx.lineWidth = 3;
                signCtx.lineCap = 'round';
                signCtx.lineJoin = 'round';
            }

            signing = true;
            const rect = signCanvas.getBoundingClientRect();

            let x, y;
            if (e.touches) {
                e.preventDefault();
                x = e.touches[0].clientX - rect.left;
                y = e.touches[0].clientY - rect.top;
            } else {
                x = e.clientX - rect.left;
                y = e.clientY - rect.top;
            }

            // Scale to canvas coordinates
            x = (x * signCanvas.width) / rect.width;
            y = (y * signCanvas.height) / rect.height;

            signCtx.beginPath();
            signCtx.moveTo(x, y);

            console.log('✅ Started drawing at:', x, y);

            // Update status and hide prompt
            const statusMain = document.getElementById('signatureStatus');
            const statusIcon = document.getElementById('statusIcon');
            const prompt = document.getElementById('signaturePrompt');

            if (statusMain) {
                statusMain.textContent = 'חותם...';
                statusMain.className = 'status-main-clean signing';
            }

            if (statusIcon) {
                statusIcon.className = 'status-icon-clean signing';
                statusIcon.innerHTML = '<i class="fas fa-circle-notch"></i>';
            }

            if (prompt) {
                prompt.classList.add('hidden');
            }
        }

        function drawSign(e) {
            if (!signing || !signCanvas || !signCtx) return;

            const rect = signCanvas.getBoundingClientRect();

            let x, y;
            if (e.touches) {
                e.preventDefault();
                x = e.touches[0].clientX - rect.left;
                y = e.touches[0].clientY - rect.top;
            } else {
                x = e.clientX - rect.left;
                y = e.clientY - rect.top;
            }

            // Scale to canvas coordinates
            x = (x * signCanvas.width) / rect.width;
            y = (y * signCanvas.height) / rect.height;

            // Draw line
            signCtx.lineTo(x, y);
            signCtx.stroke();
            signCtx.beginPath();
            signCtx.moveTo(x, y);
        }

        function stopSign() {
            if (signing) {
                console.log('🖊️ SIGNATURE COMPLETED!');
                signing = false;

                // Update status
                updateSignStatus();

                // Save signature data
                if (signCanvas) {
                    const signatureData = signCanvas.toDataURL();
                    console.log('✅ Signature saved successfully');
                }

                // Show success effect
                showBeautifulSignatureSuccess();
            }
        }

        function updateSignStatus() {
            console.log('📝 Updating signature status...');

            const statusMain = document.getElementById('signatureStatus');
            const statusIcon = document.getElementById('statusIcon');
            const dateElement = document.getElementById('poa-signatureDate-final');

            // Update status text
            if (statusMain) {
                statusMain.textContent = 'נחתם בהצלחה';
                statusMain.className = 'status-main-clean signed';
            }

            // Update status icon
            if (statusIcon) {
                statusIcon.className = 'status-icon-clean signed';
                statusIcon.innerHTML = '<i class="fas fa-check-circle"></i>';
            }

            // Update date
            if (dateElement) {
                const now = new Date();
                const hebrewDate = now.toLocaleDateString('he-IL');
                dateElement.textContent = hebrewDate;
                dateElement.classList.add('signed');
            }

            // Update proceed button
            updateProceedButton();

            console.log('✅ Status updated successfully');
        }

        function clearSign() {
            console.log('🧹 CLEARING SIGNATURE!');
            if (!signCanvas) {
                signCanvas = document.getElementById('digitalSignatureCanvas');
                if (!signCanvas) return;
                signCtx = signCanvas.getContext('2d');
            }

            // Simple clear
            signCtx.clearRect(0, 0, signCanvas.width, signCanvas.height);

            // Reset status
            const statusMain = document.getElementById('signatureStatus');
            const statusIcon = document.getElementById('statusIcon');
            const dateElement = document.getElementById('poa-signatureDate-final');
            const prompt = document.getElementById('signaturePrompt');

            if (statusMain) {
                statusMain.textContent = 'ממתין לחתימה';
                statusMain.className = 'status-main-clean waiting';
            }

            if (statusIcon) {
                statusIcon.className = 'status-icon-clean waiting';
                statusIcon.innerHTML = '<i class="fas fa-circle"></i>';
            }

            if (dateElement) {
                dateElement.textContent = '__/__/____';
                dateElement.classList.remove('signed');
            }

            // Show prompt again
            if (prompt) {
                prompt.classList.remove('hidden');
            }

            updateProceedButton();
            console.log('✅ Signature cleared successfully');
        }

        function isSignatureEmpty() {
            if (!signCanvas || !signCtx) return true;

            const imageData = signCtx.getImageData(0, 0, signCanvas.width, signCanvas.height);
            const data = imageData.data;

            // Check if any pixel is not white/transparent
            for (let i = 0; i < data.length; i += 4) {
                if (data[i] !== 255 || data[i + 1] !== 255 || data[i + 2] !== 255 || data[i + 3] !== 0) {
                    return false;
                }
            }
            return true;
        }

        function initializeDigitalSignature() {
            console.log('🚀 Initializing fixed signature...');

            // Test that canvas exists
            setTimeout(() => {
                const testCanvas = document.getElementById('digitalSignatureCanvas');
                if (testCanvas) {
                    console.log('✅ Canvas found:', testCanvas);
                    console.log('Canvas size:', testCanvas.width, 'x', testCanvas.height);

                    // Test drawing a line to verify it works
                    const testCtx = testCanvas.getContext('2d');
                    testCtx.strokeStyle = '#ff0000';
                    testCtx.lineWidth = 2;
                    testCtx.beginPath();
                    testCtx.moveTo(10, 10);
                    testCtx.lineTo(100, 50);
                    testCtx.stroke();
                    console.log('✅ Test line drawn - signature should work!');

                    // Clear test line after 2 seconds
                    setTimeout(() => {
                        testCtx.clearRect(0, 0, testCanvas.width, testCanvas.height);
                    }, 2000);
                } else {
                    console.error('❌ Canvas not found!');
                }
            }, 1000);

            setupCheckboxes();
        }

        function setupCheckboxes() {
            // Setup unified agreement checkbox
            const unifiedCheckbox = document.getElementById('unifiedAgreement');
            if (unifiedCheckbox) {
                unifiedCheckbox.addEventListener('change', updateProceedButton);
                console.log('✅ Unified agreement checkbox setup');
            }
        }

        function showBeautifulSignatureSuccess() {
            // Create beautiful success effect
            const successOverlay = document.createElement('div');
            successOverlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(16, 185, 129, 0.1);
                z-index: 999;
                pointer-events: none;
                animation: successFlash 0.6s ease-out;
            `;

            document.body.appendChild(successOverlay);

            // Create floating notification
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 30px;
                right: 30px;
                background: linear-gradient(135deg, #10b981 0%, #059669 100%);
                color: white;
                padding: 20px 28px;
                border-radius: 16px;
                box-shadow: 0 12px 40px rgba(16, 185, 129, 0.4);
                z-index: 1001;
                font-weight: 600;
                animation: beautifulSlideIn 0.6s cubic-bezier(0.34, 1.56, 0.64, 1);
                max-width: 350px;
                border: 2px solid rgba(255, 255, 255, 0.3);
                backdrop-filter: blur(20px);
            `;

            notification.innerHTML = `
                <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 10px;">
                    <div style="width: 32px; height: 32px; background: rgba(255, 255, 255, 0.25); border-radius: 50%; display: flex; align-items: center; justify-content: center; animation: checkBounce 0.8s ease-out 0.3s both;">
                        <i class="fas fa-check" style="font-size: 16px; color: white;"></i>
                    </div>
                    <div>
                        <div style="font-size: 1.2em; font-weight: 700; margin-bottom: 2px;">חתימה הושלמה!</div>
                        <div style="font-size: 0.9em; opacity: 0.9;">החתימה נשמרה בטופס 8832</div>
                    </div>
                </div>
                <div style="display: flex; align-items: center; gap: 8px; font-size: 0.85em; opacity: 0.8;">
                    <i class="fas fa-shield-alt"></i>
                    <span>מאובטח ומוצפן</span>
                </div>
            `;

            document.body.appendChild(notification);

            // Remove overlay quickly
            setTimeout(() => {
                successOverlay.remove();
            }, 600);

            // Remove notification after delay
            setTimeout(() => {
                notification.style.animation = 'beautifulSlideOut 0.5s cubic-bezier(0.4, 0, 0.2, 1)';
                setTimeout(() => notification.remove(), 500);
            }, 4500);
        }

        function showSignatureSuccessNotification() {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #10b981, #059669);
                color: white;
                padding: 16px 24px;
                border-radius: 12px;
                box-shadow: 0 10px 30px rgba(16, 185, 129, 0.3);
                z-index: 1001;
                font-weight: 600;
                animation: slideInSuccess 0.5s cubic-bezier(0.4, 0, 0.2, 1);
                max-width: 320px;
                border: 2px solid rgba(255, 255, 255, 0.2);
                backdrop-filter: blur(10px);
            `;

            notification.innerHTML = `
                <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 8px;">
                    <div style="width: 24px; height: 24px; background: rgba(255, 255, 255, 0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                        <i class="fas fa-check" style="font-size: 14px;"></i>
                    </div>
                    <span style="font-size: 1.1em;">חתימה הושלמה בהצלחה!</span>
                </div>
                <div style="font-size: 0.9em; opacity: 0.9; margin-right: 36px;">
                    החתימה נשמרה בטופס 8832 הרשמי
                </div>
            `;

            document.body.appendChild(notification);

            // Remove after 4 seconds
            setTimeout(() => {
                notification.style.animation = 'slideOutSuccess 0.5s cubic-bezier(0.4, 0, 0.2, 1)';
                setTimeout(() => notification.remove(), 500);
            }, 4000);
        }

        // Add beautiful animations
        const beautifulAnimations = document.createElement('style');
        beautifulAnimations.textContent = `
            @keyframes beautifulSlideIn {
                from {
                    transform: translateX(100%) translateY(-20px) scale(0.8);
                    opacity: 0;
                }
                to {
                    transform: translateX(0) translateY(0) scale(1);
                    opacity: 1;
                }
            }

            @keyframes beautifulSlideOut {
                from {
                    transform: translateX(0) translateY(0) scale(1);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%) translateY(-20px) scale(0.8);
                    opacity: 0;
                }
            }

            @keyframes successFlash {
                0% { opacity: 0; }
                50% { opacity: 1; }
                100% { opacity: 0; }
            }

            @keyframes checkBounce {
                0% { transform: scale(0); }
                50% { transform: scale(1.3); }
                100% { transform: scale(1); }
            }

            .signature-canvas-beautiful:hover {
                filter: brightness(1.02) contrast(1.05);
                transform: scale(1.002);
            }

            .signature-background.active .signature-canvas-beautiful {
                filter: brightness(1.05) contrast(1.1);
            }

            /* Beautiful transitions for all elements */
            .signature-pad,
            .signature-background,
            .status-icon,
            .status-text,
            .date-value,
            .signature-prompt {
                transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            }

            /* Smooth hover effects */
            .clear-btn-beautiful:hover {
                filter: brightness(1.1);
            }

            .beautiful-signature-container:hover .signature-canvas-beautiful {
                filter: brightness(1.01);
            }
        `;
        document.head.appendChild(beautifulAnimations);

        function updateProceedButton() {
            const proceedBtn = document.getElementById('proceedToCalculator');
            if (!proceedBtn) return;

            // Check unified agreement checkbox
            const unifiedCheckbox = document.getElementById('unifiedAgreement');
            const agreementChecked = unifiedCheckbox && unifiedCheckbox.checked;

            // Check signature
            const signed = signCanvas ? !isSignatureEmpty() : false;

            if (agreementChecked && signed) {
                proceedBtn.disabled = false;
                proceedBtn.innerHTML = `
                    <div class="btn-icon-clean">
                        <i class="fas fa-rocket"></i>
                    </div>
                    <div class="btn-content-clean">
                        <div class="btn-title-clean">המשך לחישוב מס</div>
                        <div class="btn-subtitle-clean">כל האישורים הושלמו ✓ מוכן לחישוב!</div>
                    </div>
                `;
                proceedBtn.style.background = 'linear-gradient(135deg, #10b981, #059669)';
                proceedBtn.style.boxShadow = '0 8px 24px rgba(16, 185, 129, 0.3)';

                // Add click handler for proceeding to tax calculation
                proceedBtn.onclick = function() {
                    proceedToTaxCalculation();
                };
            } else if (agreementChecked && !signed) {
                proceedBtn.disabled = true;
                proceedBtn.innerHTML = `
                    <div class="btn-icon-clean">
                        <i class="fas fa-signature"></i>
                    </div>
                    <div class="btn-content-clean">
                        <div class="btn-title-clean">המשך לחישוב מס</div>
                        <div class="btn-subtitle-clean">נדרשת חתימה דיגיטלית על טופס 8832</div>
                    </div>
                `;
                proceedBtn.style.background = 'linear-gradient(135deg, #f59e0b, #d97706)';
                proceedBtn.style.boxShadow = '0 4px 16px rgba(245, 158, 11, 0.2)';
                proceedBtn.onclick = null;
            } else if (!agreementChecked && signed) {
                proceedBtn.disabled = true;
                proceedBtn.innerHTML = `
                    <div class="btn-icon-clean">
                        <i class="fas fa-check-square"></i>
                    </div>
                    <div class="btn-content-clean">
                        <div class="btn-title-clean">המשך לחישוב מס</div>
                        <div class="btn-subtitle-clean">נדרש אישור התנאים וההסכמות</div>
                    </div>
                `;
                proceedBtn.style.background = 'linear-gradient(135deg, #f59e0b, #d97706)';
                proceedBtn.style.boxShadow = '0 4px 16px rgba(245, 158, 11, 0.2)';
                proceedBtn.onclick = null;
            } else {
                proceedBtn.disabled = true;
                proceedBtn.innerHTML = `
                    <div class="btn-icon-clean">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="btn-content-clean">
                        <div class="btn-title-clean">המשך לחישוב מס</div>
                        <div class="btn-subtitle-clean">נדרש אישור התנאים וחתימה דיגיטלית</div>
                    </div>
                `;
                proceedBtn.style.background = 'linear-gradient(135deg, #9ca3af, #6b7280)';
                proceedBtn.style.boxShadow = '0 4px 12px rgba(156, 163, 175, 0.2)';
                proceedBtn.onclick = null;
            }
        }

        function proceedToTaxCalculation() {
            console.log('🚀 Proceeding to tax calculation...');

            // Show loading state
            const proceedBtn = document.getElementById('proceedToCalculator');
            if (proceedBtn) {
                proceedBtn.innerHTML = `
                    <div class="btn-icon-clean">
                        <i class="fas fa-spinner fa-spin"></i>
                    </div>
                    <div class="btn-content-clean">
                        <div class="btn-title-clean">מעבר לחישוב מס...</div>
                        <div class="btn-subtitle-clean">טוען נתונים...</div>
                    </div>
                `;
                proceedBtn.disabled = true;
            }

            // Simulate processing and move to tax calculation
            setTimeout(() => {
                // Hide current step
                const currentStep = document.getElementById('step2');
                if (currentStep) {
                    currentStep.style.display = 'none';
                }

                // Show tax calculation step
                const taxStep = document.getElementById('step3');
                if (taxStep) {
                    taxStep.style.display = 'block';
                    taxStep.scrollIntoView({ behavior: 'smooth' });
                }

                // Update progress
                updateProgress(3);

                // Calculate tax
                calculateTax();

                console.log('✅ Successfully moved to tax calculation step');
            }, 1500);
        }





        // Add animations
        const animationStyle = document.createElement('style');
        animationStyle.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(animationStyle);















        // Initialize system
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing...');
            loadUserPreferences();

            // Initialize signature after page loads
            setTimeout(() => {
                initializeDigitalSignature();
            }, 1000);
        });
    </script>
</body>
</html>
