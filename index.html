<!DOCTYPE html>
<html lang="he" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TaxFree Pro - תנאי שימוש והסכמות | מערכת החזרי מס מקצועית</title>
    <meta name="description" content="תנאי שימוש והסכמות למערכת TaxFree Pro - שירותי החזר מס מקצועיים עם טופס 8832">
    <meta name="keywords" content="החזר מס, מס הכנסה, טופס 8832, יפוי כח, רשות המסים, ישראל">
    <meta name="author" content="TaxFree Pro">
    <meta property="og:title" content="TaxFree Pro - מערכת החזרי מס מקצועית">
    <meta property="og:description" content="מערכת מתקדמת לחישוב והגשת החזרי מס עם תמיכה מלאה בחקיקה הישראלית">
    <meta property="og:type" content="website">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>💰</text></svg>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Heebo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        :root {
            /* Primary Brand Colors */
            --primary-color: #667eea;
            --primary-dark: #5a67d8;
            --primary-light: #a5b4fc;
            --secondary-color: #764ba2;
            --secondary-dark: #6b46c1;
            --secondary-light: #c4b5fd;
            --accent-color: #f093fb;
            --accent-dark: #e879f9;
            --accent-light: #fdf4ff;

            /* Status Colors */
            --success-color: #10b981;
            --success-dark: #059669;
            --success-light: #d1fae5;
            --warning-color: #f59e0b;
            --warning-dark: #d97706;
            --warning-light: #fef3c7;
            --error-color: #ef4444;
            --error-dark: #dc2626;
            --error-light: #fee2e2;
            --info-color: #3b82f6;
            --info-dark: #2563eb;
            --info-light: #dbeafe;

            /* Neutral Colors */
            --gray-50: #f9fafb;
            --gray-100: #f3f4f6;
            --gray-200: #e5e7eb;
            --gray-300: #d1d5db;
            --gray-400: #9ca3af;
            --gray-500: #6b7280;
            --gray-600: #4b5563;
            --gray-700: #374151;
            --gray-800: #1f2937;
            --gray-900: #111827;

            /* Theme variables */
            --bg-primary: #ffffff;
            --bg-secondary: #f8f9fa;
            --bg-tertiary: var(--gray-100);
            --text-primary: #333333;
            --text-secondary: #6b7280;
            --text-tertiary: var(--gray-400);
            --border-color: #e5e7eb;
            --shadow-color: rgba(0, 0, 0, 0.1);

            /* Spacing Scale */
            --space-1: 0.25rem;
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 1rem;
            --space-5: 1.25rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --space-10: 2.5rem;
            --space-12: 3rem;
            --space-16: 4rem;
            --space-20: 5rem;

            /* Typography Scale */
            --text-xs: 0.75rem;
            --text-sm: 0.875rem;
            --text-base: 1rem;
            --text-lg: 1.125rem;
            --text-xl: 1.25rem;
            --text-2xl: 1.5rem;
            --text-3xl: 1.875rem;
            --text-4xl: 2.25rem;
            --text-5xl: 3rem;

            /* Border Radius */
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --radius-2xl: 1.5rem;
            --radius-full: 9999px;

            /* Shadows */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
            --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

            /* Transitions */
            --transition-fast: 0.15s ease-in-out;
            --transition-normal: 0.3s ease-in-out;
            --transition-slow: 0.5s ease-in-out;

            /* Z-Index Scale */
            --z-base: 0;
            --z-dropdown: 1000;
            --z-sticky: 1020;
            --z-fixed: 1030;
            --z-modal: 1050;
            --z-tooltip: 1070;
        }

        [data-theme="dark"] {
            --bg-primary: #1a1a1a;
            --bg-secondary: #2d2d2d;
            --text-primary: #ffffff;
            --text-secondary: #a0a0a0;
            --border-color: #404040;
            --shadow-color: rgba(255, 255, 255, 0.1);
            --primary-color: #8b9aff;
            --success-color: #34d399;
            --warning-color: #fbbf24;
            --error-color: #f87171;
        }

        [data-theme="high-contrast"] {
            --bg-primary: #ffffff;
            --bg-secondary: #f0f0f0;
            --text-primary: #000000;
            --text-secondary: #333333;
            --border-color: #000000;
            --primary-color: #0000ff;
            --success-color: #008000;
            --warning-color: #ff8000;
            --error-color: #ff0000;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Heebo', 'Segoe UI', 'Arial Hebrew', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            background-attachment: fixed;
            min-height: 100vh;
            color: var(--text-primary);
            padding: 0;
            line-height: 1.7;
            scroll-behavior: smooth;
            font-size: var(--text-base);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        /* Accessibility Improvements */
        *:focus {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
        }

        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* Skip to content link */
        .skip-link {
            position: absolute;
            top: -40px;
            left: 6px;
            background: var(--primary-color);
            color: white;
            padding: 8px;
            text-decoration: none;
            border-radius: var(--radius-md);
            z-index: var(--z-tooltip);
            transition: top var(--transition-fast);
        }

        .skip-link:focus {
            top: 6px;
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            *,
            *::before,
            *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
                scroll-behavior: auto !important;
            }
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
            :root {
                --primary-color: #0000ff;
                --success-color: #008000;
                --warning-color: #ff8000;
                --error-color: #ff0000;
                --text-primary: #000000;
                --bg-primary: #ffffff;
                --border-color: #000000;
            }
        }

        .header {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            padding: 15px 0;
            box-shadow: 0 4px 30px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 1000;
            border-bottom: 1px solid rgba(255,255,255,0.2);
        }

        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }

        .logo {
            font-size: 2.5em;
            font-weight: 800;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            display: flex;
            align-items: center;
            gap: 15px;
            text-shadow: 0 4px 8px rgba(0,0,0,0.1);
            letter-spacing: -1px;
        }

        .logo-icon {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 15px;
            padding: 12px;
            font-size: 1.2em;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
            animation: logoFloat 3s ease-in-out infinite;
        }

        @keyframes logoFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
        }

        .nav-menu {
            display: flex;
            gap: 30px;
            list-style: none;
        }

        .nav-menu a {
            text-decoration: none;
            color: #333;
            font-weight: 500;
            transition: color 0.3s;
        }

        .nav-menu a:hover {
            color: #667eea;
        }



        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }



        .calculator-section {
            background: transparent;
            padding: 20px 0;
            color: white;
        }

        .calculator-container {
            background: rgba(255, 255, 255, 0.98);
            border-radius: 25px;
            padding: 50px;
            color: #333;
            box-shadow:
                0 25px 50px rgba(0,0,0,0.15),
                0 0 0 1px rgba(255,255,255,0.1),
                inset 0 1px 0 rgba(255,255,255,0.9);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255,255,255,0.2);
            position: relative;
            overflow: hidden;
        }

        .calculator-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #667eea);
            background-size: 200% 100%;
            animation: gradientShift 3s ease infinite;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        /* Terms Navigation Styles */
        .terms-header-section {
            margin-bottom: 30px;
        }

        .terms-navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .back-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            font-size: 14px;
        }

        .back-btn:hover {
            background: linear-gradient(135deg, #495057, #343a40);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .terms-progress-info {
            background: rgba(102, 126, 234, 0.1);
            padding: 8px 15px;
            border-radius: 20px;
            color: #667eea;
            font-weight: 600;
            font-size: 14px;
        }

        .terms-progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }

        .terms-progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
            border-radius: 10px;
            transition: width 0.5s ease;
            width: 0%;
            position: relative;
        }

        .terms-progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: progressShimmer 2s infinite;
        }

        @keyframes progressShimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* Final Actions Styles */
        .final-actions {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 20px;
            padding: 40px;
            margin: 40px 0;
            border: 2px solid #e9ecef;
            box-shadow: 0 10px 30px rgba(0,0,0,0.08);
        }

        .action-summary {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }

        .summary-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #28a745, #20c997);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        }

        .summary-text h4 {
            margin: 0 0 8px 0;
            color: #2c3e50;
            font-size: 1.4em;
            font-weight: 700;
        }

        .summary-text p {
            margin: 0;
            color: #6c757d;
            font-size: 1.1em;
        }

        .action-buttons {
            display: flex;
            gap: 20px;
            justify-content: center;
            flex-wrap: wrap;
        }

        .secondary-btn {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            background: linear-gradient(135deg, #6c757d, #495057);
            color: white;
            padding: 15px 25px;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            font-size: 16px;
            min-width: 180px;
            justify-content: center;
        }

        .secondary-btn:hover {
            background: linear-gradient(135deg, #495057, #343a40);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }

        .proceed-btn {
            display: inline-flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 20px 30px;
            border: none;
            border-radius: 25px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            font-size: 18px;
            min-width: 250px;
            position: relative;
            overflow: hidden;
        }

        .proceed-btn:disabled {
            background: linear-gradient(135deg, #adb5bd, #6c757d);
            cursor: not-allowed;
            transform: none;
        }

        .proceed-btn:not(:disabled):hover {
            background: linear-gradient(135deg, #764ba2, #f093fb);
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }

        .proceed-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .proceed-btn:not(:disabled):hover::before {
            left: 100%;
        }

        .btn-subtitle {
            font-size: 12px;
            font-weight: 400;
            opacity: 0.9;
        }

        /* Responsive Design for Actions */
        @media (max-width: 768px) {
            .action-summary {
                flex-direction: column;
                text-align: center;
            }

            .action-buttons {
                flex-direction: column;
                align-items: center;
            }

            .secondary-btn,
            .proceed-btn {
                width: 100%;
                max-width: 300px;
            }

            .final-actions {
                padding: 25px;
                margin: 20px 0;
            }
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 30px 0;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 10px;
            font-weight: 700;
            color: #2c3e50;
            font-size: 1.1em;
            position: relative;
        }

        .form-group label::after {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 0;
            width: 30px;
            height: 2px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 1px;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 16px 20px;
            border: 2px solid #e5e7eb;
            border-radius: 15px;
            font-size: 16px;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
        }

        .form-group input:hover,
        .form-group select:hover {
            border-color: #9ca3af;
        }

        .checkbox-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .checkbox-item {
            display: flex;
            align-items: center;
            padding: 12px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            transition: background-color 0.3s;
        }

        .checkbox-item:hover {
            background: #e9ecef;
        }

        .checkbox-item input[type="checkbox"] {
            width: auto;
            margin-left: 10px;
        }

        .calculate-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            color: white;
            padding: 20px 60px;
            border: none;
            border-radius: 50px;
            font-size: 1.4em;
            font-weight: 800;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            width: 100%;
            margin: 40px 0;
            position: relative;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .calculate-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .calculate-btn:hover::before {
            left: 100%;
        }

        .calculate-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 20px 45px rgba(102, 126, 234, 0.4);
        }

        .calculate-btn:active {
            transform: translateY(-1px);
        }

        .result-section {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 50%, #d299c2 100%);
            padding: 50px;
            border-radius: 25px;
            margin: 40px 0;
            display: none;
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
            border: 1px solid rgba(255,255,255,0.3);
            position: relative;
            overflow: hidden;
        }

        .result-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1) 25%, transparent 25%, transparent 75%, rgba(255,255,255,0.1) 75%);
            background-size: 20px 20px;
            animation: shimmer 20s linear infinite;
        }

        @keyframes shimmer {
            0% { background-position: 0 0; }
            100% { background-position: 40px 40px; }
        }

        .result-section h3 {
            color: #2c3e50;
            font-size: 2em;
            font-weight: 800;
            margin-bottom: 30px;
            text-align: center;
            position: relative;
            z-index: 1;
        }

        .result-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 20px 25px;
            margin: 10px 0;
            background: rgba(255,255,255,0.8);
            border-radius: 15px;
            font-size: 1.2em;
            font-weight: 600;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.3);
            transition: all 0.3s ease;
            position: relative;
            z-index: 1;
        }

        .result-item:hover {
            transform: translateX(5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }

        .result-item.warning {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 2px solid #f59e0b;
            color: #92400e;
            font-weight: 600;
        }

        .result-item.warning:hover {
            background: linear-gradient(135deg, #fde68a 0%, #fbbf24 100%);
            transform: translateX(3px);
        }

        .result-item:last-child {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            font-weight: 800;
            font-size: 1.5em;
            padding: 25px;
            box-shadow: 0 15px 35px rgba(39, 174, 96, 0.3);
            animation: resultPulse 2s ease-in-out infinite;
        }

        @keyframes resultPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
        }

        /* Form Section Titles */
        .form-section-title {
            color: #2c3e50;
            font-size: 1.6em;
            font-weight: 800;
            margin-bottom: 25px;
            padding: 15px 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 15px;
            border-left: 5px solid #667eea;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            display: flex;
            align-items: center;
            gap: 10px;
            position: relative;
            overflow: hidden;
        }

        .form-section-title::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
        }

        /* Personal Details Step Styles */
        .step-container {
            margin-bottom: 40px;
        }

        .step-header {
            text-align: center;
            margin-bottom: 40px;
            position: relative;
        }

        .step-number {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            font-size: 1.8em;
            font-weight: 800;
            border-radius: 50%;
            margin-bottom: 20px;
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
            animation: stepPulse 2s ease-in-out infinite;
        }

        @keyframes stepPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .step-header h3 {
            font-size: 2.2em;
            color: #2c3e50;
            margin: 15px 0 10px 0;
            font-weight: 800;
        }

        .step-header p {
            color: #6b7280;
            font-size: 1.2em;
            margin-bottom: 0;
        }

        .personal-form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin: 30px 0;
        }

        .field-error {
            color: #e74c3c;
            font-size: 0.9em;
            margin-top: 5px;
            font-weight: 600;
            display: none;
        }

        .form-group.error input,
        .form-group.error select {
            border-color: #e74c3c;
            box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
        }

        .form-group.error .field-error {
            display: block;
        }

        .privacy-notice {
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f9ff 100%);
            border: 2px solid #10b981;
            border-radius: 15px;
            padding: 20px;
            margin: 30px 0;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .privacy-icon {
            font-size: 2em;
            color: #10b981;
        }

        .privacy-text {
            color: #065f46;
            font-size: 1.1em;
            line-height: 1.5;
        }

        .continue-btn {
            background: linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%);
            color: white;
            padding: 20px 50px;
            border: none;
            border-radius: 50px;
            font-size: 1.4em;
            font-weight: 800;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            width: 100%;
            margin: 30px 0;
            position: relative;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(16, 185, 129, 0.3);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .continue-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .continue-btn:hover::before {
            left: 100%;
        }

        .continue-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 20px 45px rgba(16, 185, 129, 0.4);
        }

        .continue-btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .back-btn {
            background: linear-gradient(135deg, #6b7280, #4b5563);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            margin-top: 15px;
            transition: all 0.3s ease;
        }

        .back-btn:hover {
            background: linear-gradient(135deg, #4b5563, #374151);
            transform: translateY(-1px);
        }

        /* Terms of Service Styles - Simplified */
        .terms-container {
            background: rgba(255, 255, 255, 0.98);
            border-radius: 25px;
            margin: 20px 0;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            position: relative;
            padding: 40px;
        }

        .terms-progress-bar {
            background: #e5e7eb;
            height: 8px;
            border-radius: 10px;
            margin: 20px 0 40px 0;
            overflow: hidden;
            position: relative;
        }

        .terms-progress-fill {
            background: linear-gradient(90deg, #667eea, #764ba2, #10b981);
            height: 100%;
            width: 0%;
            border-radius: 10px;
            transition: width 0.5s ease;
            position: relative;
        }

        .terms-progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .progress-text {
            text-align: center;
            margin-top: 10px;
            font-weight: 600;
            color: #667eea;
        }

        .terms-section {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-radius: 20px;
            margin: 30px 0;
            padding: 35px;
            border: 1px solid #e9ecef;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .terms-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(180deg, #667eea, #764ba2, #f093fb);
            transition: width 0.3s ease;
        }

        .terms-section:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 50px rgba(0,0,0,0.15);
            border-color: #667eea;
        }

        .terms-section:hover::before {
            width: 6px;
        }

        .terms-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }

        .terms-icon {
            font-size: 2em;
            width: 60px;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            color: white;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .terms-header h4 {
            color: #2c3e50;
            font-size: 1.5em;
            font-weight: 700;
            margin: 0;
            flex: 1;
        }

        .terms-content {
            color: #495057;
            line-height: 1.7;
            font-size: 1.05em;
        }

        .terms-content ul {
            list-style: none;
            padding: 0;
        }

        .terms-content li {
            padding: 12px 0;
            border-bottom: 1px solid #f1f3f4;
            display: flex;
            align-items: flex-start;
            gap: 12px;
        }

        .terms-content li::before {
            content: '✓';
            color: #28a745;
            font-weight: bold;
            font-size: 1.2em;
            margin-top: 2px;
        }

        .terms-content li:last-child {
            border-bottom: none;
        }

        .legal-explanation {
            background: #e0f2fe;
            border: 2px solid #0288d1;
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            position: relative;
        }

        .legal-explanation::before {
            content: '⚖️';
            position: absolute;
            top: -15px;
            right: 20px;
            background: #0288d1;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2em;
        }

        .legal-title {
            color: #01579b;
            font-weight: 800;
            font-size: 1.2em;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .legal-content {
            color: #0277bd;
            line-height: 1.7;
            font-weight: 500;
        }

        .legal-content ul {
            margin: 15px 0;
            padding-right: 25px;
        }

        .legal-content li {
            margin: 10px 0;
            position: relative;
        }

        .legal-content li::before {
            content: '📋';
            position: absolute;
            right: -25px;
            top: 0;
        }

        .expand-section {
            background: #f0f9ff;
            border: 2px dashed #0ea5e9;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .expand-section:hover {
            background: #e0f2fe;
            border-color: #0284c7;
            transform: scale(1.02);
        }

        .expand-btn {
            background: linear-gradient(135deg, #0ea5e9, #0284c7);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 25px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .expand-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(14, 165, 233, 0.3);
        }

        .terms-header {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e5e7eb;
        }

        .terms-icon {
            font-size: 2em;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .terms-header h4 {
            color: #2c3e50;
            font-size: 1.5em;
            font-weight: 700;
            margin: 0;
        }

        .terms-content {
            color: #374151;
            line-height: 1.6;
        }

        .terms-content ul {
            padding-right: 20px;
            margin: 15px 0;
        }

        .terms-content li {
            margin: 8px 0;
            position: relative;
        }

        .pricing-highlight {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 20px 0;
            text-align: center;
            box-shadow: 0 10px 30px rgba(16, 185, 129, 0.3);
        }

        .pricing-main {
            font-size: 2em;
            font-weight: 800;
            margin-bottom: 15px;
        }

        .pricing-benefits {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .benefit {
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9em;
        }

        .important-note {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            color: #856404;
            font-weight: 600;
        }

        .small-text {
            font-size: 0.9em;
            color: #6b7280;
            font-style: italic;
        }

        /* Agreement Section */
        .agreement-section {
            background: linear-gradient(135deg, #e0e7ff 0%, #f0f4ff 100%);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            border: 2px solid #667eea;
        }

        .agreement-section h4 {
            color: #2c3e50;
            font-size: 1.8em;
            font-weight: 800;
            margin-bottom: 25px;
            text-align: center;
        }

        .checkbox-agreements {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .agreement-item {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .agreement-item:hover {
            background: rgba(255, 255, 255, 0.95);
            border-color: #667eea;
            transform: translateX(5px);
        }

        .agreement-item input[type="checkbox"] {
            display: none;
        }

        .checkmark {
            width: 24px;
            height: 24px;
            border: 3px solid #d1d5db;
            border-radius: 6px;
            position: relative;
            transition: all 0.3s ease;
            flex-shrink: 0;
            margin-top: 2px;
        }

        .agreement-item input[type="checkbox"]:checked + .checkmark {
            background: linear-gradient(135deg, #10b981, #059669);
            border-color: #10b981;
        }

        .agreement-item input[type="checkbox"]:checked + .checkmark::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-weight: bold;
            font-size: 16px;
        }

        /* Agreement Section Styles */
        .agreement-section {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            border: 3px solid #f59e0b;
        }

        .agreement-section h4 {
            color: #92400e;
            font-size: 1.8em;
            font-weight: 800;
            margin-bottom: 15px;
            text-align: center;
        }

        .agreement-checkboxes {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin: 30px 0;
        }

        .checkbox-item {
            display: flex;
            align-items: flex-start;
            gap: 15px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 15px;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .checkbox-item:hover {
            background: rgba(255, 255, 255, 0.95);
            border-color: #f59e0b;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(245, 158, 11, 0.2);
        }

        .checkbox-item input[type="checkbox"] {
            width: 20px;
            height: 20px;
            margin: 0;
            cursor: pointer;
            accent-color: #f59e0b;
        }

        .checkbox-item label {
            display: flex;
            align-items: center;
            gap: 12px;
            cursor: pointer;
            font-weight: 600;
            color: #374151;
            line-height: 1.5;
            margin: 0;
        }

        .checkbox-item label i {
            color: #f59e0b;
            font-size: 1.2em;
        }

        .checkbox-item input[type="checkbox"]:checked + label {
            color: #059669;
        }

        .checkbox-item input[type="checkbox"]:checked + label i {
            color: #059669;
        }

        .agreement-note {
            display: flex;
            gap: 15px;
            padding: 20px;
            background: rgba(59, 130, 246, 0.1);
            border-radius: 15px;
            border-left: 4px solid #3b82f6;
            margin: 25px 0;
        }

        .note-icon {
            color: #3b82f6;
            font-size: 1.3em;
            margin-top: 2px;
        }

        .note-text {
            color: #1e40af;
            font-weight: 600;
            line-height: 1.5;
        }

        .signature-container {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 20px;
            padding: 40px;
            margin: 40px 0;
            text-align: center;
            box-shadow: 0 15px 40px rgba(0,0,0,0.1);
            border: 3px dashed #d1d5db;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .signature-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
            transform: translateX(-100%);
            transition: transform 0.3s ease;
        }

        .signature-container.active {
            border-color: #667eea;
            box-shadow: 0 20px 50px rgba(102, 126, 234, 0.2);
            transform: translateY(-2px);
        }

        .signature-container.active::before {
            transform: translateX(0);
        }

        #signatureCanvas {
            border: 3px solid #e5e7eb;
            border-radius: 15px;
            cursor: crosshair;
            display: block;
            margin: 0 auto;
            max-width: 100%;
            background: #ffffff;
            box-shadow: inset 0 2px 8px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }

        #signatureCanvas:hover {
            border-color: #667eea;
            box-shadow: inset 0 2px 8px rgba(0,0,0,0.05), 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .signature-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 30px;
            padding-top: 25px;
            border-top: 2px solid #e5e7eb;
            flex-wrap: wrap;
            gap: 15px;
        }

        .clear-btn {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            padding: 15px 25px;
            border: none;
            border-radius: 30px;
            font-size: 1em;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            gap: 8px;
            box-shadow: 0 4px 15px rgba(239, 68, 68, 0.2);
        }

        .clear-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(239, 68, 68, 0.4);
            background: linear-gradient(135deg, #dc2626, #b91c1c);
        }

        .clear-btn:active {
            transform: translateY(-1px);
        }

        .signature-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        #signatureStatus {
            font-weight: 700;
            font-size: 1.1em;
            padding: 12px 20px;
            border-radius: 25px;
            background: rgba(239, 68, 68, 0.1);
            color: #ef4444;
            transition: all 0.3s ease;
            border: 2px solid rgba(239, 68, 68, 0.2);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        #signatureStatus.signed {
            background: rgba(16, 185, 129, 0.1);
            color: #10b981;
            border-color: rgba(16, 185, 129, 0.2);
        }

        /* Signature Instructions */
        .signature-instructions {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin: 25px 0;
            padding: 20px;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 15px;
            border: 1px solid rgba(102, 126, 234, 0.1);
            flex-wrap: wrap;
        }

        .instruction-item {
            display: flex;
            align-items: center;
            gap: 10px;
            color: #667eea;
            font-weight: 600;
            font-size: 0.95em;
        }

        .instruction-item i {
            font-size: 1.2em;
            color: #667eea;
        }

        /* Responsive signature */
        @media (max-width: 768px) {
            .signature-instructions {
                flex-direction: column;
                gap: 15px;
                text-align: center;
            }

            .instruction-item {
                justify-content: center;
            }

            #signatureCanvas {
                width: 100%;
                height: 150px;
            }

            .signature-container {
                padding: 25px 15px;
            }

            .signature-controls {
                flex-direction: column;
                gap: 15px;
            }
        }

        .signature-details {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            text-align: right;
        }

        .signature-meta {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .signature-meta span {
            background: #f3f4f6;
            padding: 8px 12px;
            border-radius: 8px;
            font-size: 0.9em;
            font-weight: 600;
        }

        .proceed-btn {
            background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 50%, #4c1d95 100%);
            color: white;
            padding: 20px 50px;
            border: none;
            border-radius: 50px;
            font-size: 1.4em;
            font-weight: 800;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            width: 100%;
            margin: 30px 0;
            position: relative;
            overflow: hidden;
            box-shadow: 0 15px 35px rgba(124, 58, 237, 0.3);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .proceed-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .proceed-btn:hover::before {
            left: 100%;
        }

        .proceed-btn:hover:not(:disabled) {
            transform: translateY(-3px);
            box-shadow: 0 20px 45px rgba(124, 58, 237, 0.4);
        }

        .proceed-btn:disabled {
            background: #9ca3af;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* Form 8832 Styles */
        .form-8832-container {
            background: white;
            border: 3px solid #2c3e50;
            border-radius: 15px;
            margin: 25px 0;
            font-family: 'Times New Roman', serif;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .form-header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 25px;
            border-radius: 12px 12px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
        }

        .form-logo {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .israel-emblem {
            font-size: 3em;
            background: linear-gradient(135deg, #0066cc, #ffffff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .form-title h3 {
            font-size: 1.8em;
            font-weight: 800;
            margin: 0;
            color: white;
        }

        .form-title h4 {
            font-size: 1.4em;
            font-weight: 600;
            margin: 5px 0;
            color: #bdc3c7;
        }

        .form-title h5 {
            font-size: 1.2em;
            font-weight: 600;
            margin: 5px 0;
            color: #ecf0f1;
        }

        .form-number {
            text-align: left;
            font-size: 1.1em;
            font-weight: 600;
        }

        .form-number span {
            display: block;
            margin: 5px 0;
        }

        .form-content {
            padding: 30px;
        }

        .form-section-title {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            padding: 15px 20px;
            margin: 25px -10px 20px -10px;
            font-size: 1.3em;
            font-weight: 700;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }

        .form-fields {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .form-field {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .form-field label {
            font-weight: 700;
            color: #2c3e50;
            font-size: 1.1em;
        }

        .field-value {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 12px 15px;
            font-size: 1.1em;
            font-weight: 600;
            color: #495057;
            min-height: 20px;
        }

        .authorization-scope {
            background: #e8f5e8;
            border: 2px solid #28a745;
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
        }

        .scope-items {
            margin: 20px 0;
        }

        .scope-item {
            display: flex;
            gap: 15px;
            margin: 15px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 10px;
            border-left: 4px solid #28a745;
        }

        .scope-number {
            background: #28a745;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 800;
            flex-shrink: 0;
        }

        .scope-content {
            line-height: 1.6;
            color: #2c3e50;
        }

        .limitations {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
        }

        .limitation-item {
            display: flex;
            gap: 15px;
            margin: 15px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 10px;
            border-left: 4px solid #ffc107;
        }

        .limitation-icon {
            font-size: 1.5em;
            flex-shrink: 0;
        }

        .limitation-text {
            line-height: 1.6;
            color: #856404;
            font-weight: 600;
        }

        .validity-section {
            background: #e7f3ff;
            border: 2px solid #007bff;
            border-radius: 12px;
            padding: 25px;
            margin: 20px 0;
        }

        .validity-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 15px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 10px;
            border-left: 4px solid #007bff;
        }

        .validity-item label {
            font-weight: 700;
            color: #2c3e50;
        }

        .validity-date {
            font-weight: 600;
            color: #007bff;
        }

        .form-footer {
            border-top: 3px solid #2c3e50;
            padding-top: 25px;
            margin-top: 30px;
        }

        .signature-area {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
        }

        .signature-field {
            text-align: center;
        }

        .signature-field label {
            display: block;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .signature-line {
            display: block;
            border-bottom: 2px solid #2c3e50;
            min-height: 40px;
            padding: 10px;
            font-weight: 600;
            color: #495057;
        }

        /* Power of Attorney Final Section */
        .power-of-attorney-final-section {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 3px solid #f59e0b;
            border-radius: 20px;
            padding: 30px;
            margin: 40px 0;
            position: relative;
        }

        .power-of-attorney-final-section::before {
            content: '📜';
            position: absolute;
            top: -20px;
            right: 30px;
            background: #f59e0b;
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5em;
            box-shadow: 0 5px 15px rgba(245, 158, 11, 0.3);
        }

        .power-of-attorney-final-section h4 {
            color: #92400e;
            font-size: 1.8em;
            font-weight: 800;
            margin-bottom: 20px;
            text-align: center;
        }

        /* Important note and form actions */
        .important-note {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 2px solid #f59e0b;
            border-radius: 15px;
            padding: 20px;
            margin: 25px 0;
            color: #92400e;
            font-weight: 600;
            line-height: 1.6;
            position: relative;
        }

        .important-note::before {
            content: '⚠️';
            position: absolute;
            top: -15px;
            right: 20px;
            background: #f59e0b;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2em;
        }

        .form-actions {
            text-align: center;
            margin: 25px 0;
        }

        .form-actions .expand-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-weight: 700;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }

        .form-actions .expand-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        /* Form signature styles */
        .signature-field-canvas {
            display: flex;
            flex-direction: column;
            gap: 10px;
            width: 100%;
        }

        .signature-field-canvas label {
            font-weight: 700;
            color: #2c3e50;
            font-size: 1.1em;
        }



        .signature-instruction {
            text-align: center;
            margin-top: 10px;
        }

        .instruction-title {
            color: #2c3e50;
            font-weight: bold;
            font-size: 1em;
            margin-bottom: 8px;
        }

        .instruction-details {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }

        .instruction-details span {
            color: #6b7280;
            font-size: 0.85em;
            background: rgba(102, 126, 234, 0.1);
            padding: 4px 8px;
            border-radius: 8px;
            border: 1px solid rgba(102, 126, 234, 0.2);
        }

        /* Form Signature Styles for 8832 */
        .form-signature-container {
            border: 3px solid #2c3e50;
            border-radius: 10px;
            background: #ffffff;
            padding: 10px;
            position: relative;
            box-shadow: inset 0 2px 5px rgba(0,0,0,0.1);
        }

        #formSignatureCanvas {
            display: block;
            cursor: crosshair;
            border-radius: 5px;
            background: #ffffff;
            width: 100%;
            max-width: 400px;
            height: 120px;
            border: 1px solid #e5e7eb;
        }

        .form-signature-controls {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 10px;
            padding-top: 10px;
            border-top: 1px solid #e5e7eb;
        }

        .clear-form-btn {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 20px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 0.9em;
        }

        .clear-form-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(239, 68, 68, 0.3);
        }

        .form-signature-status {
            font-weight: 600;
            font-size: 0.9em;
            padding: 5px 10px;
            border-radius: 15px;
            background: rgba(239, 68, 68, 0.1);
            color: #ef4444;
        }

        .form-signature-status.signed {
            background: rgba(16, 185, 129, 0.1);
            color: #10b981;
        }

        .form-signature-container.active {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        #formSignatureCanvas:hover {
            border-color: #667eea;
        }

        .signature-area {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin-top: 20px;
        }

        .signature-field {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .signature-field label {
            font-weight: 700;
            color: #2c3e50;
            min-width: 80px;
        }

        /* Comprehensive Agreement Styles */
        .comprehensive-agreement-section {
            background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
            border: 3px solid #0288d1;
            border-radius: 25px;
            padding: 40px;
            margin: 40px 0;
            position: relative;
        }

        .comprehensive-agreement-section::before {
            content: '📋';
            position: absolute;
            top: -25px;
            right: 40px;
            background: #0288d1;
            color: white;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2em;
            box-shadow: 0 8px 20px rgba(2, 136, 209, 0.3);
        }

        .comprehensive-agreement-section h4 {
            color: #01579b;
            font-size: 2em;
            font-weight: 800;
            margin-bottom: 25px;
            text-align: center;
        }

        .comprehensive-agreement-content {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
            padding: 30px;
            margin: 25px 0;
            box-shadow: inset 0 2px 10px rgba(0,0,0,0.05);
        }

        .agreement-section-detailed {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border-right: 5px solid #0288d1;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }

        .agreement-section-detailed:hover {
            transform: translateX(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.12);
            border-right-color: #10b981;
        }

        .agreement-section-detailed h5 {
            color: #01579b;
            font-size: 1.3em;
            font-weight: 700;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .agreement-section-detailed ul {
            margin: 15px 0;
            padding-right: 25px;
        }

        .agreement-section-detailed li {
            margin: 12px 0;
            line-height: 1.6;
            color: #2c3e50;
            font-weight: 500;
            position: relative;
        }

        .agreement-section-detailed li::before {
            content: '✓';
            position: absolute;
            right: -25px;
            top: 0;
            color: #10b981;
            font-weight: bold;
            font-size: 1.1em;
        }

        .final-agreement-checkbox {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            border: 3px solid #f59e0b;
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            position: relative;
        }

        .final-agreement-checkbox::before {
            content: '⚖️';
            position: absolute;
            top: -20px;
            right: 30px;
            background: #f59e0b;
            color: white;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5em;
            box-shadow: 0 5px 15px rgba(245, 158, 11, 0.3);
        }

        .comprehensive-agreement-item {
            display: flex;
            align-items: flex-start;
            gap: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .comprehensive-agreement-item:hover {
            transform: translateX(5px);
        }

        .comprehensive-agreement-item input[type="checkbox"] {
            display: none;
        }

        .comprehensive-checkmark {
            width: 35px;
            height: 35px;
            border: 4px solid #f59e0b;
            border-radius: 8px;
            position: relative;
            transition: all 0.3s ease;
            flex-shrink: 0;
            margin-top: 5px;
            background: white;
        }

        .comprehensive-agreement-item input[type="checkbox"]:checked + .comprehensive-checkmark {
            background: linear-gradient(135deg, #10b981, #059669);
            border-color: #10b981;
            transform: scale(1.1);
        }

        .comprehensive-agreement-item input[type="checkbox"]:checked + .comprehensive-checkmark::after {
            content: '✓';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-weight: bold;
            font-size: 20px;
        }

        .agreement-text {
            color: #92400e;
            font-size: 1.1em;
            line-height: 1.7;
            font-weight: 600;
        }

        /* Theme Toggle Styles */
        .theme-toggle {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1000;
            background: var(--bg-primary);
            border: 2px solid var(--border-color);
            border-radius: 50px;
            padding: 12px 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px var(--shadow-color);
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .theme-toggle:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px var(--shadow-color);
        }

        .theme-icon {
            font-size: 1.2em;
            transition: transform 0.3s ease;
        }

        .theme-toggle:hover .theme-icon {
            transform: rotate(180deg);
        }

        /* Enhanced Accessibility Controls */
        .accessibility-controls {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: var(--z-fixed);
            display: flex;
            flex-direction: column;
            gap: var(--space-2);
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: var(--space-1);
        }

        .control-btn {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: var(--radius-full);
            padding: var(--space-3) var(--space-4);
            cursor: pointer;
            transition: all var(--transition-normal);
            display: flex;
            align-items: center;
            gap: var(--space-2);
            font-weight: 600;
            color: var(--text-primary);
            box-shadow: var(--shadow-md);
            font-size: var(--text-sm);
            min-width: 120px;
            justify-content: flex-start;
        }

        .control-btn:hover {
            background: rgba(255, 255, 255, 1);
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        .control-btn:focus {
            outline: 2px solid var(--primary-color);
            outline-offset: 2px;
        }

        .help-btn {
            background: linear-gradient(135deg, var(--info-color), var(--info-dark));
            color: white;
            border-color: var(--info-color);
        }

        .help-btn:hover {
            background: linear-gradient(135deg, var(--info-dark), var(--info-color));
        }

        /* Enhanced Theme States */
        [data-theme="dark"] .control-btn {
            background: rgba(31, 41, 55, 0.95);
            color: white;
            border-color: rgba(255, 255, 255, 0.2);
        }

        [data-font-size="large"] {
            font-size: 1.125rem;
        }

        [data-reading-mode="true"] {
            background: #f5f5dc !important;
            color: #2c3e50 !important;
        }

        /* Icon animations */
        .font-icon,
        .contrast-icon,
        .reading-icon,
        .help-icon {
            font-size: 1.2em;
            transition: transform var(--transition-normal);
        }

        .control-btn:hover .font-icon {
            transform: scale(1.2);
        }

        .control-btn:hover .contrast-icon {
            transform: rotateY(180deg);
        }

        .control-btn:hover .reading-icon {
            transform: rotateX(180deg);
        }

        .control-btn:hover .help-icon {
            transform: rotate(360deg);
        }

        /* Auto-save indicator */
        .auto-save-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: var(--success-color);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 600;
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.3s ease;
        }

        .auto-save-indicator.show {
            opacity: 1;
            transform: translateY(0);
        }

        /* Help Modal Styles */
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: var(--z-modal);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: var(--bg-primary);
            border-radius: var(--radius-2xl);
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow: hidden;
            box-shadow: var(--shadow-2xl);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: scale(0.9) translateY(-20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--space-6);
            border-bottom: 1px solid var(--border-color);
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
        }

        .modal-header h2 {
            margin: 0;
            display: flex;
            align-items: center;
            gap: var(--space-3);
            font-size: var(--text-2xl);
        }

        .close-btn {
            background: none;
            border: none;
            color: white;
            font-size: var(--text-xl);
            cursor: pointer;
            padding: var(--space-2);
            border-radius: var(--radius-md);
            transition: background var(--transition-fast);
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .modal-body {
            padding: var(--space-6);
            max-height: 60vh;
            overflow-y: auto;
        }

        .help-tabs {
            display: flex;
            gap: var(--space-2);
            margin-bottom: var(--space-6);
            border-bottom: 1px solid var(--border-color);
            flex-wrap: wrap;
        }

        .tab-btn {
            background: none;
            border: none;
            padding: var(--space-3) var(--space-4);
            cursor: pointer;
            border-radius: var(--radius-lg) var(--radius-lg) 0 0;
            transition: all var(--transition-fast);
            display: flex;
            align-items: center;
            gap: var(--space-2);
            font-weight: 600;
            color: var(--text-secondary);
        }

        .tab-btn.active {
            background: var(--primary-color);
            color: white;
        }

        .tab-btn:hover:not(.active) {
            background: var(--gray-100);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        .help-steps {
            display: flex;
            flex-direction: column;
            gap: var(--space-4);
        }

        .help-step {
            display: flex;
            gap: var(--space-4);
            padding: var(--space-4);
            background: var(--gray-50);
            border-radius: var(--radius-lg);
            border-left: 4px solid var(--primary-color);
        }

        .step-number {
            width: 40px;
            height: 40px;
            background: var(--primary-color);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            flex-shrink: 0;
        }

        .step-content h4 {
            margin: 0 0 var(--space-2) 0;
            color: var(--text-primary);
        }

        .step-content p {
            margin: 0;
            color: var(--text-secondary);
            line-height: 1.6;
        }

        .faq-list {
            display: flex;
            flex-direction: column;
            gap: var(--space-3);
        }

        .faq-item {
            border: 1px solid var(--border-color);
            border-radius: var(--radius-lg);
            overflow: hidden;
        }

        .faq-question {
            width: 100%;
            background: none;
            border: none;
            padding: var(--space-4);
            text-align: right;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: 600;
            transition: background var(--transition-fast);
        }

        .faq-question:hover {
            background: var(--gray-50);
        }

        .faq-answer {
            padding: 0 var(--space-4) var(--space-4) var(--space-4);
            display: none;
            color: var(--text-secondary);
            line-height: 1.6;
        }

        .contact-info,
        .accessibility-features {
            display: flex;
            flex-direction: column;
            gap: var(--space-4);
        }

        .contact-item,
        .feature-item {
            display: flex;
            gap: var(--space-4);
            padding: var(--space-4);
            background: var(--gray-50);
            border-radius: var(--radius-lg);
            align-items: flex-start;
        }

        .contact-item i,
        .feature-item i {
            color: var(--primary-color);
            font-size: var(--text-xl);
            margin-top: var(--space-1);
        }

        .modal-footer {
            display: flex;
            justify-content: space-between;
            padding: var(--space-6);
            border-top: 1px solid var(--border-color);
            background: var(--gray-50);
        }

        .btn-secondary,
        .btn-primary {
            padding: var(--space-3) var(--space-6);
            border: none;
            border-radius: var(--radius-lg);
            font-weight: 600;
            cursor: pointer;
            transition: all var(--transition-fast);
            display: flex;
            align-items: center;
            gap: var(--space-2);
        }

        .btn-secondary {
            background: var(--gray-200);
            color: var(--text-primary);
        }

        .btn-secondary:hover {
            background: var(--gray-300);
        }

        .btn-primary {
            background: var(--primary-color);
            color: white;
        }

        .btn-primary:hover {
            background: var(--primary-dark);
        }

        /* Responsive Modal */
        @media (max-width: 768px) {
            .modal-content {
                width: 95%;
                max-height: 95vh;
            }

            .modal-header,
            .modal-body,
            .modal-footer {
                padding: var(--space-4);
            }

            .help-tabs {
                flex-direction: column;
            }

            .tab-btn {
                justify-content: center;
            }

            .help-step {
                flex-direction: column;
                text-align: center;
            }

            .modal-footer {
                flex-direction: column;
                gap: var(--space-3);
            }

            .btn-secondary,
            .btn-primary {
                justify-content: center;
            }
        }

        /* Recovery dialog */
        .recovery-dialog {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 2000;
            display: none;
            align-items: center;
            justify-content: center;
        }

        .recovery-content {
            background: var(--bg-primary);
            border-radius: 20px;
            padding: 30px;
            max-width: 500px;
            margin: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .recovery-content h3 {
            color: var(--text-primary);
            margin-bottom: 15px;
            font-size: 1.5em;
        }

        .recovery-content p {
            color: var(--text-secondary);
            margin-bottom: 20px;
            line-height: 1.6;
        }

        .recovery-buttons {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
        }

        .recovery-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .recovery-btn.primary {
            background: var(--primary-color);
            color: white;
        }

        .recovery-btn.secondary {
            background: var(--bg-secondary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
        }



        .section-title {
            text-align: center;
            font-size: 3em;
            margin-bottom: 15px;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 800;
            letter-spacing: -1px;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
        }

        .section-subtitle {
            text-align: center;
            font-size: 1.3em;
            color: #555;
            margin-bottom: 50px;
            font-weight: 400;
            line-height: 1.6;
            max-width: 600px;
            margin-left: auto;
            margin-right: auto;
        }

        /* OCR Styles */
        .ocr-section {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 40px;
            border-radius: 20px;
            margin-bottom: 40px;
            border: 2px solid transparent;
            background-clip: padding-box;
            position: relative;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .ocr-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border-radius: 20px;
            padding: 2px;
            background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
            mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            mask-composite: exclude;
            z-index: -1;
        }

        .ocr-section h3 {
            color: #2c3e50;
            font-size: 1.8em;
            font-weight: 700;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .ocr-section p {
            color: #555;
            font-size: 1.1em;
            margin-bottom: 25px;
            line-height: 1.6;
        }

        .upload-area {
            border: 3px dashed #d1d5db;
            border-radius: 20px;
            padding: 50px;
            text-align: center;
            background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            cursor: pointer;
            margin: 25px 0;
            position: relative;
            overflow: hidden;
        }

        .upload-area::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1), transparent);
            transition: left 0.5s;
        }

        .upload-area:hover::before {
            left: 100%;
        }

        .upload-area:hover,
        .upload-area.dragover {
            border-color: #667eea;
            background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
            transform: translateY(-2px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.2);
        }

        .upload-content {
            pointer-events: none;
        }

        .upload-icon {
            font-size: 5em;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: uploadPulse 2s ease-in-out infinite;
        }

        @keyframes uploadPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .upload-content h4 {
            font-size: 1.5em;
            color: #2c3e50;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .upload-content p {
            color: #6b7280;
            font-size: 1.1em;
            margin-bottom: 25px;
        }

        .upload-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            color: white;
            padding: 15px 35px;
            border: none;
            border-radius: 30px;
            font-size: 1.2em;
            font-weight: 600;
            cursor: pointer;
            margin-top: 15px;
            pointer-events: all;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.4);
        }

        .document-types {
            margin: 25px 0;
        }

        .doc-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }

        .doc-item {
            background: white;
            padding: 10px;
            border-radius: 8px;
            font-size: 0.9em;
            border: 1px solid #e0e0e0;
        }

        .uploaded-files {
            margin: 20px 0;
        }

        .file-item {
            background: white;
            padding: 15px;
            border-radius: 10px;
            margin: 10px 0;
            border: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .file-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .file-status {
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            font-weight: bold;
        }

        .status-processing {
            background: #fff3cd;
            color: #856404;
        }

        .status-completed {
            background: #d4edda;
            color: #155724;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
        }

        .ocr-progress {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s;
        }

        .ocr-results {
            background: white;
            padding: 25px;
            border-radius: 10px;
            margin: 20px 0;
            border: 2px solid #27ae60;
        }

        .extracted-data-item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #e0e0e0;
        }

        .apply-data-btn {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            margin-top: 20px;
            width: 100%;
        }

        .divider {
            text-align: center;
            margin: 50px 0;
            position: relative;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, #667eea, transparent);
        }

        .divider span {
            background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
            padding: 15px 30px;
            color: #2c3e50;
            font-weight: 700;
            font-size: 1.1em;
            border-radius: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border: 2px solid #e5e7eb;
        }

        /* Fallback and Modal Styles */
        .fallback-options {
            background: #fff3cd;
            border: 2px solid #ffc107;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .fallback-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 20px 0;
        }

        .fallback-btn {
            background: linear-gradient(135deg, #ffc107, #ff8c00);
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .fallback-btn:hover {
            transform: translateY(-2px);
        }

        .tips {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
        }

        .tips ul {
            margin: 10px 0;
            padding-right: 20px;
        }

        .tips li {
            margin: 5px 0;
        }

        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal-content {
            background: white;
            padding: 30px;
            border-radius: 15px;
            max-width: 500px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }

        .interactive-form {
            margin: 20px 0;
        }

        .interactive-form .form-group {
            margin: 15px 0;
        }

        .interactive-form label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
        }

        .interactive-form input,
        .interactive-form select {
            width: 100%;
            padding: 10px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
        }

        .modal-buttons {
            display: flex;
            gap: 15px;
            margin-top: 25px;
        }

        .submit-btn {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 1.1em;
            cursor: pointer;
            flex: 1;
        }

        .cancel-btn {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 1.1em;
            cursor: pointer;
            flex: 1;
        }

        @keyframes pulse {
            0% { box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.7); }
            70% { box-shadow: 0 0 0 10px rgba(255, 107, 107, 0); }
            100% { box-shadow: 0 0 0 0 rgba(255, 107, 107, 0); }
        }

        /* Checkbox containers for exemptions */
        .checkbox-container {
            display: flex;
            align-items: center;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 12px;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
            background: #f9fafb;
        }

        .checkbox-container:hover {
            background: #f3f4f6;
            border-color: #7c3aed;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(124, 58, 237, 0.1);
        }

        .checkbox-text {
            margin-right: 10px;
            font-weight: 500;
            color: #374151;
            font-size: 0.95em;
        }

        .checkbox-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .info-box {
            background: #f0f9ff;
            border: 1px solid #0ea5e9;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }

        .info-box h4 {
            color: #0369a1;
            margin: 0 0 10px 0;
            font-size: 1.1em;
        }

        .info-box ul {
            margin: 0;
            padding-right: 20px;
            color: #374151;
        }

        .info-box li {
            margin-bottom: 5px;
        }

        @media (max-width: 768px) {
            .hero h1 {
                font-size: 2.5em;
            }

            .nav-menu {
                display: none;
            }

            .form-grid {
                grid-template-columns: 1fr;
            }

            .doc-grid {
                grid-template-columns: 1fr;
            }

            .checkbox-grid {
                grid-template-columns: 1fr;
                gap: 10px;
            }

            .checkbox-container {
                padding: 10px;
            }
        }

        /* Professional Header Styles */
        .professional-header {
            background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            color: white;
            padding: 20px 0;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
        }

        .logo-section {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo-icon {
            width: 60px;
            height: 60px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        .logo-text h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 700;
            background: linear-gradient(45deg, #fff, #e8f4fd);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .logo-text p {
            margin: 0;
            font-size: 14px;
            opacity: 0.9;
        }

        .header-badges {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }

        .badge {
            display: flex;
            align-items: center;
            gap: 8px;
            background: rgba(255,255,255,0.15);
            padding: 8px 15px;
            border-radius: 25px;
            font-size: 13px;
            font-weight: 500;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }

        .badge.secure { background: rgba(46, 204, 113, 0.2); }
        .badge.certified { background: rgba(241, 196, 15, 0.2); }
        .badge.support { background: rgba(155, 89, 182, 0.2); }

        /* Progress Steps */
        .progress-container {
            background: rgba(255,255,255,0.95);
            padding: 20px 0;
            border-bottom: 1px solid rgba(0,0,0,0.1);
        }

        .progress-steps {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 40px;
            flex-wrap: wrap;
        }

        .step {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
            position: relative;
            opacity: 0.5;
            transition: all 0.3s ease;
        }

        .step.completed,
        .step.active {
            opacity: 1;
        }

        .step-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            background: #e9ecef;
            color: #6c757d;
            transition: all 0.3s ease;
        }

        .step.completed .step-icon {
            background: #28a745;
            color: white;
        }

        .step.active .step-icon {
            background: #007bff;
            color: white;
            box-shadow: 0 0 20px rgba(0,123,255,0.3);
        }

        .step-text {
            font-size: 12px;
            font-weight: 600;
            text-align: center;
            color: #495057;
        }

        .step.completed .step-text {
            color: #28a745;
        }

        .step.active .step-text {
            color: #007bff;
        }

        /* Main Content Styles */
        .main-content {
            padding: 40px 0;
            min-height: calc(100vh - 200px);
        }

        .welcome-section {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255,255,255,0.3);
        }

        .welcome-content {
            text-align: center;
        }

        .main-title {
            font-size: 2.5em;
            color: #2c3e50;
            margin-bottom: 15px;
            font-weight: 700;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .main-title i {
            color: #3498db;
            font-size: 0.9em;
        }

        .main-subtitle {
            font-size: 1.2em;
            color: #6c757d;
            margin-bottom: 25px;
            line-height: 1.6;
        }

        .client-info {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            background: linear-gradient(135deg, #e8f5e8, #f0f9ff);
            padding: 12px 20px;
            border-radius: 25px;
            color: #2c3e50;
            font-weight: 600;
            border: 2px solid #28a745;
        }

        .client-info i {
            color: #28a745;
            font-size: 1.2em;
        }

        /* Terms Container */
        .terms-main-container {
            background: rgba(255,255,255,0.98);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 15px 50px rgba(0,0,0,0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255,255,255,0.3);
            position: relative;
            overflow: hidden;
        }

        .terms-main-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
            background-size: 200% 100%;
            animation: gradientShift 3s ease infinite;
        }

        /* Responsive Design for Header */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                text-align: center;
            }

            .logo-text h1 {
                font-size: 24px;
            }

            .progress-steps {
                gap: 20px;
            }

            .step-icon {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }

            .step-text {
                font-size: 11px;
            }

            .main-title {
                font-size: 2em;
                flex-direction: column;
                gap: 10px;
            }

            .welcome-section,
            .terms-main-container {
                padding: 25px;
                margin: 15px;
            }
        }
    </style>
</head>
<body>
    <!-- Professional Header -->
    <div class="professional-header">
        <div class="container">
            <div class="header-content">
                <div class="logo-section">
                    <div class="logo-icon">
                        <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="logo-text">
                        <h1>TaxFree Pro</h1>
                        <p>מערכת החזרי מס מקצועית</p>
                    </div>
                </div>
                <div class="header-badges">
                    <div class="badge secure">
                        <i class="fas fa-lock"></i>
                        <span>מאובטח</span>
                    </div>
                    <div class="badge certified">
                        <i class="fas fa-certificate"></i>
                        <span>מוסמך</span>
                    </div>
                    <div class="badge support">
                        <i class="fas fa-headset"></i>
                        <span>תמיכה 24/7</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Progress Indicator -->
    <div class="progress-container">
        <div class="container">
            <div class="progress-steps">
                <div class="step completed">
                    <div class="step-icon"><i class="fas fa-user-check"></i></div>
                    <div class="step-text">פרטים אישיים</div>
                </div>
                <div class="step active">
                    <div class="step-icon"><i class="fas fa-file-contract"></i></div>
                    <div class="step-text">תנאי שימוש</div>
                </div>
                <div class="step">
                    <div class="step-icon"><i class="fas fa-calculator"></i></div>
                    <div class="step-text">חישוב מס</div>
                </div>
                <div class="step">
                    <div class="step-icon"><i class="fas fa-paper-plane"></i></div>
                    <div class="step-text">שליחה</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Accessibility & Theme Controls -->
    <div class="accessibility-controls">
        <div class="control-group">
            <button class="control-btn" onclick="toggleTheme()" aria-label="החלף נושא">
                <span class="theme-icon">🌙</span>
                <span class="theme-text">מצב לילה</span>
            </button>

            <button class="control-btn" onclick="toggleFontSize()" aria-label="שנה גודל גופן">
                <span class="font-icon">🔤</span>
                <span class="font-text">גופן גדול</span>
            </button>

            <button class="control-btn" onclick="toggleHighContrast()" aria-label="ניגודיות גבוהה">
                <span class="contrast-icon">⚫</span>
                <span class="contrast-text">ניגודיות</span>
            </button>

            <button class="control-btn" onclick="toggleReadingMode()" aria-label="מצב קריאה">
                <span class="reading-icon">📖</span>
                <span class="reading-text">קריאה</span>
            </button>
        </div>

        <div class="help-button">
            <button class="control-btn help-btn" onclick="showHelp()" aria-label="עזרה ותמיכה">
                <span class="help-icon">❓</span>
                <span class="help-text">עזרה</span>
            </button>
        </div>
    </div>

    <!-- Auto-save Indicator -->
    <div class="auto-save-indicator" id="autoSaveIndicator">
        💾 נשמר אוטומטית
    </div>

    <!-- Recovery Dialog -->
    <div class="recovery-dialog" id="recoveryDialog">
        <div class="recovery-content">
            <h3>🔄 שחזור נתונים</h3>
            <p>נמצאה טיוטה שנשמרה אוטומטית. האם תרצה לשחזר את הנתונים ולהמשיך מהמקום שבו עצרת?</p>
            <div class="recovery-buttons">
                <button class="recovery-btn secondary" onclick="dismissRecovery()">התחל מחדש</button>
                <button class="recovery-btn primary" onclick="restoreFromDraft()">שחזר נתונים</button>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main class="main-content">
        <div class="container">
            <!-- Welcome Section -->
            <div class="welcome-section">
                <div class="welcome-content">
                    <h1 class="main-title">
                        <i class="fas fa-file-contract"></i>
                        תנאי שימוש והסכמות
                    </h1>
                    <p class="main-subtitle">
                        אנא קרא בעיון את תנאי השימוש וטופס 8832 לפני המשך התהליך
                    </p>
                    <div class="client-info" id="clientWelcome">
                        <i class="fas fa-user-circle"></i>
                        <span>טוען פרטי לקוח...</span>
                    </div>
                </div>
            </div>

            <!-- Terms of Service Container -->
            <div class="terms-main-container">
                <div class="terms-header-section">
                    <div class="terms-navigation">
                        <button type="button" class="back-btn" onclick="goBackToTerms()">
                            <i class="fas fa-arrow-right"></i>
                            חזור לפרטים אישיים
                        </button>
                        <div class="terms-progress-info">
                            <span class="progress-text" id="progressText">התקדמות: 0 מתוך 6 סעיפים הושלמו</span>
                        </div>
                    </div>

                    <div class="terms-progress-bar">
                        <div class="terms-progress-fill" id="termsProgress"></div>
                    </div>
                </div>

                <div class="terms-container">
                        <div class="terms-progress-bar">
                            <div class="terms-progress-fill" id="termsProgress"></div>
                        </div>
                        <div class="progress-text" id="progressText">התקדמות: 0 מתוך 6 סעיפים הושלמו</div>
                        <!-- Service Overview -->
                        <div class="terms-section" id="service-section">
                            <div class="terms-header">
                                <div class="terms-icon">🔍</div>
                                <h4>סקירת השירות המקצועי</h4>
                            </div>
                            <div class="terms-content">
                                <p><strong>TaxFree Pro</strong> מספקת שירותי ייעוץ מס מקצועיים ומורשים על פי חוק רגולציה של שירותים פיננסיים:</p>

                                <div class="legal-explanation">
                                    <div class="legal-title">📋 הסבר משפטי - מהות השירות</div>
                                    <div class="legal-content">
                                        <p><strong>בסיס חוקי:</strong> השירות ניתן על פי חוק מס הכנסה התשכ"א-1961 וחוק רגולציה של שירותים פיננסיים התשע"ו-2016.</p>
                                        <p><strong>סוג השירות:</strong> ייעוץ מס מקצועי והגשת בקשות להחזר מס בהתאם לסעיף 161 לחוק מס הכנסה.</p>
                                        <p><strong>רישוי:</strong> השירות מוענק על ידי עורכי דין מורשים בעלי רישיון פעיל מלשכת עורכי הדין בישראל.</p>
                                    </div>
                                </div>

                                <h5>🛠️ רכיבי השירות המקצועי:</h5>
                                <ul>
                                    <li><strong>ניתוח מסמכים דיגיטלי:</strong> זיהוי אוטומטי של מסמכי מס באמצעות טכנולוגיית OCR מתקדמת</li>
                                    <li><strong>חישובי מס מדויקים:</strong> על פי טבלאות מס הכנסה הרשמיות של רשות המסים לשנת 2024-2025</li>
                                    <li><strong>הגשה דיגיטלית מאובטחת:</strong> העברת בקשות להחזר מס באמצעות מערכת שומה דיגיטלית</li>
                                    <li><strong>מעקב משפטי:</strong> ניטור סטטוס הבקשה ותיאום עם רשות המסים עד לקבלת ההחזר</li>
                                    <li><strong>ייעוץ מקצועי:</strong> תמיכה משפטית ומקצועית לאורך כל התהליך</li>
                                </ul>

                                <div class="legal-explanation">
                                    <div class="legal-title">⚖️ התחייבויות משפטיות</div>
                                    <div class="legal-content">
                                        <ul>
                                            <li><strong>חובת נאמנות:</strong> פעילות בהתאם לאתיקה המקצועית של לשכת עורכי הדין</li>
                                            <li><strong>חובת זהירות:</strong> בדיקה מקצועית של כל המסמכים והנתונים</li>
                                            <li><strong>חובת דיווח:</strong> עדכון שוטף על התקדמות הטיפול בתיק</li>
                                            <li><strong>חובת סודיות:</strong> שמירה על סודיות מקצועית מוחלטת</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Pricing Structure -->
                        <div class="terms-section" id="pricing-section">
                            <div class="terms-header">
                                <div class="terms-icon">💰</div>
                                <h4>מבנה תמחור שקוף ומוסדר</h4>
                            </div>
                            <div class="terms-content">
                                <div class="legal-explanation">
                                    <div class="legal-title">📋 הסבר משפטי - בסיס התמחור</div>
                                    <div class="legal-content">
                                        <p><strong>בסיס חוקי:</strong> התמחור נקבע בהתאם לתקנות לשכת עורכי הדין בדבר שכר טרחה לשירותי מס (תקנה 15א).</p>
                                        <p><strong>עקרון "Success Fee":</strong> תשלום מותנה הצלחה בהתאם לפסיקת בית המשפט העליון (ע"א 2605/05).</p>
                                        <p><strong>שקיפות:</strong> חובת גילוי מלא של מבנה התמחור בהתאם לחוק הגנת הצרכן התשמ"א-1981.</p>
                                    </div>
                                </div>

                                <div class="pricing-highlight">
                                    <div class="pricing-main">15% מגובה ההחזר בלבד</div>
                                    <div class="pricing-benefits">
                                        <span class="benefit">✅ ללא דמי פתיחת תיק</span>
                                        <span class="benefit">✅ ללא תשלום מראש</span>
                                        <span class="benefit">✅ תשלום רק על הצלחה</span>
                                        <span class="benefit">✅ כולל מע"מ</span>
                                    </div>
                                </div>

                                <div class="legal-explanation">
                                    <div class="legal-title">💰 פירוט התמחור המשפטי</div>
                                    <div class="legal-content">
                                        <ul>
                                            <li><strong>שיעור התמחור:</strong> 15% מסכום ההחזר הגולמי לפני ניכוי מסים</li>
                                            <li><strong>תנאי תשלום:</strong> התשלום יבוצע רק לאחר זיכוי ההחזר בחשבון הבנק של הלקוח</li>
                                            <li><strong>זמני החזר לפי חוק:</strong> רשות המסים מחויבת להחזיר תוך 45 ימים מהגשת הבקשה (סעיף 161א לחוק מס הכנסה)</li>
                                            <li><strong>אופן התשלום:</strong> חיוב אוטומטי או העברה בנקאית לאחר קבלת ההחזר</li>
                                            <li><strong>מע"מ:</strong> התמחור כולל מע"מ בשיעור החוקי (17%)</li>
                                            <li><strong>ביטול עסקה:</strong> אין תשלום במקרה של אי אישור הבקשה על ידי רשות המסים</li>
                                        </ul>
                                    </div>
                                </div>

                                <h5>📊 דוגמאות חישוב:</h5>
                                <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 15px 0;">
                                    <p><strong>דוגמה 1:</strong> החזר של 10,000 ₪ → תשלום 1,500 ₪ (כולל מע"מ)</p>
                                    <p><strong>דוגמה 2:</strong> החזר של 25,000 ₪ → תשלום 3,750 ₪ (כולל מע"מ)</p>
                                    <p><strong>דוגמה 3:</strong> אין החזר → אין תשלום כלל</p>
                                </div>

                                <div class="legal-explanation">
                                    <div class="legal-title">⏰ זמני החזר לפי חוק</div>
                                    <div class="legal-content">
                                        <p><strong>בהתאם לסעיף 161א לחוק מס הכנסה התשכ"א-1961:</strong></p>
                                        <ul>
                                            <li><strong>זמן חוקי:</strong> רשות המסים חייבת להחזיר תוך 45 ימים מיום הגשת הבקשה</li>
                                            <li><strong>בקשות פשוטות:</strong> החזר תוך 30 ימים במקרים רגילים</li>
                                            <li><strong>בקשות מורכבות:</strong> עד 75 ימים במקרים הדורשים בדיקה מעמיקה</li>
                                            <li><strong>ריבית פיגורים:</strong> במקרה של איחור, רשות המסים משלמת ריבית</li>
                                            <li><strong>התשלום שלנו:</strong> יבוצע תוך 3 ימי עסקים מקבלת ההחזר</li>
                                        </ul>
                                    </div>
                                </div>

                                <div class="legal-explanation">
                                    <div class="legal-title">⚖️ הגנות משפטיות ללקוח</div>
                                    <div class="legal-content">
                                        <ul>
                                            <li><strong>זכות ביטול:</strong> 14 ימים לביטול ההסכם ללא עלות (חוק הגנת הצרכן)</li>
                                            <li><strong>ביטוח אחריות מקצועית:</strong> כיסוי של עד 2 מיליון ₪ לנזקים</li>
                                            <li><strong>פיקוח מקצועי:</strong> כפיפות לפיקוח לשכת עורכי הדין</li>
                                            <li><strong>זכות תלונה:</strong> אפשרות הגשת תלונה ללשכת עורכי הדין</li>
                                            <li><strong>מעקב שקוף:</strong> עדכונים שוטפים על התקדמות הבקשה</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Power of Attorney -->
                        <div class="terms-section" id="power-of-attorney-section">
                            <div class="terms-header">
                                <div class="terms-icon">📜</div>
                                <h4>ייפוי כוח רשמי - טופס 8832</h4>
                            </div>
                            <div class="terms-content">
                                <div class="legal-explanation">
                                    <div class="legal-title">📋 הסבר משפטי - ייפוי כוח</div>
                                    <div class="legal-content">
                                        <p><strong>בסיס חוקי:</strong> ייפוי כוח על פי חוק הייפוי כוח התשכ"ה-1965 וחוק מס הכנסה התשכ"א-1961.</p>
                                        <p><strong>מטרה:</strong> מתן הרשאה חוקית לטיפול בענייני מס בשמך ובמקומך.</p>
                                        <p><strong>היקף:</strong> מוגבל אך ורק לטיפול בבקשת החזר מס לשנת המס הרלוונטית.</p>
                                        <p><strong>תוקף:</strong> עד להשלמת הטיפול בבקשה או עד 31/12/2025.</p>
                                    </div>
                                </div>

                                <div class="expand-section" onclick="togglePowerOfAttorney()">
                                    <h5>📄 צפייה בטופס 8832 המלא</h5>
                                    <p>לחץ כאן לצפייה בטופס ייפוי הכוח הרשמי</p>
                                    <button class="expand-btn">🔍 הצג טופס מלא</button>
                                    <button class="expand-btn" onclick="printPowerOfAttorney(event)">🖨️ הדפס טופס</button>
                                </div>

                                <div id="powerOfAttorneyForm" style="display: none;">
                                    <div class="legal-explanation">
                                        <div class="legal-title">⚖️ הסברים משפטיים לטופס</div>
                                        <div class="legal-content">
                                            <ul>
                                                <li><strong>סעיף 1-2:</strong> זיהוי הצדדים - המרשה (אתה) והמורשה (TaxFree Pro)</li>
                                                <li><strong>סעיף 3:</strong> היקף ההרשאות - מוגבל לענייני החזר מס בלבד</li>
                                                <li><strong>סעיף 4:</strong> הגבלות - אין הרשאה לחתימה על התחייבויות כספיות</li>
                                                <li><strong>סעיף 5:</strong> תוקף זמני - מוגבל בזמן ובהיקף</li>
                                                <li><strong>סעיף 6:</strong> זכות ביטול - ניתן לבטל בכל עת בהודעה בכתב</li>
                                            </ul>
                                        </div>
                                    </div>
                                <div class="form-8832-container">
                                    <div class="form-header">
                                        <div class="form-logo">
                                            <div class="israel-emblem">🇮🇱</div>
                                            <div class="form-title">
                                                <h3>מדינת ישראל</h3>
                                                <h4>רשות המסים</h4>
                                                <h5>טופס 8832 - ייפוי כוח</h5>
                                            </div>
                                        </div>
                                        <div class="form-number">
                                            <span>מס' טופס: 8832</span>
                                            <span>גרסה: 01/2024</span>
                                        </div>
                                    </div>

                                    <div class="form-content">
                                        <div class="form-section-title">פרטי הנותן ייפוי הכוח (המרשה)</div>

                                        <div class="form-fields">
                                            <div class="form-field">
                                                <label>שם פרטי ומשפחה:</label>
                                                <div class="field-value" id="poa-fullName">יתמלא אוטומטית</div>
                                            </div>
                                            <div class="form-field">
                                                <label>מספר זהות:</label>
                                                <div class="field-value" id="poa-idNumber">יתמלא אוטומטית</div>
                                            </div>
                                            <div class="form-field">
                                                <label>כתובת:</label>
                                                <div class="field-value">לפי הרישומים ברשות המסים</div>
                                            </div>
                                            <div class="form-field">
                                                <label>טלפון:</label>
                                                <div class="field-value" id="poa-phone">יתמלא אוטומטית</div>
                                            </div>
                                            <div class="form-field">
                                                <label>דואר אלקטרוני:</label>
                                                <div class="field-value" id="poa-email">יתמלא אוטומטית</div>
                                            </div>
                                        </div>

                                        <div class="form-section-title">פרטי המקבל ייפוי הכוח (המורשה)</div>

                                        <div class="form-fields">
                                            <div class="form-field">
                                                <label>שם החברה/עורך דין:</label>
                                                <div class="field-value">TaxFree Pro - שירותי מס מתקדמים בע"מ</div>
                                            </div>
                                            <div class="form-field">
                                                <label>מספר רישיון:</label>
                                                <div class="field-value">רישיון עורך דין מס' 12345</div>
                                            </div>
                                            <div class="form-field">
                                                <label>כתובת:</label>
                                                <div class="field-value">רחוב הטכנולוגיה 1, תל אביב</div>
                                            </div>
                                            <div class="form-field">
                                                <label>טלפון:</label>
                                                <div class="field-value">03-1234567</div>
                                            </div>
                                        </div>

                                        <div class="form-section-title">היקף ייפוי הכוח</div>

                                        <div class="authorization-scope">
                                            <p><strong>אני מרשה בזה את המורשה לפעול בשמי ובמקומי בכל הנושאים הבאים:</strong></p>

                                            <div class="scope-items">
                                                <div class="scope-item">
                                                    <span class="scope-number">1.</span>
                                                    <div class="scope-content">
                                                        <strong>הגשת בקשה להחזר מס הכנסה</strong> - לשנת המס הרלוונטית, כולל מילוי וחתימה על כל הטפסים הנדרשים
                                                    </div>
                                                </div>

                                                <div class="scope-item">
                                                    <span class="scope-number">2.</span>
                                                    <div class="scope-content">
                                                        <strong>קבלת מידע ממס הכנסה</strong> - צפייה בנתונים, קבלת אישורים ומסמכים הנוגעים לבקשת החזר המס
                                                    </div>
                                                </div>

                                                <div class="scope-item">
                                                    <span class="scope-number">3.</span>
                                                    <div class="scope-content">
                                                        <strong>מעקב וטיפול בבקשה</strong> - בדיקת סטטוס, מתן הסברים נוספים, הגשת מסמכים משלימים
                                                    </div>
                                                </div>

                                                <div class="scope-item">
                                                    <span class="scope-number">4.</span>
                                                    <div class="scope-content">
                                                        <strong>תיקון ועדכון נתונים</strong> - במידת הצורך, לצורך השלמת הבקשה או תיקון טעויות
                                                    </div>
                                                </div>

                                                <div class="scope-item">
                                                    <span class="scope-number">5.</span>
                                                    <div class="scope-content">
                                                        <strong>קבלת התכתבויות</strong> - מרשות המסים בנושא בקשת החזר המס, כולל החלטות ודרישות
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-section-title">הגבלות ייפוי הכוח</div>

                                        <div class="limitations">
                                            <div class="limitation-item">
                                                <span class="limitation-icon">⚠️</span>
                                                <div class="limitation-text">
                                                    ייפוי כוח זה מוגבל <strong>אך ורק</strong> לטיפול בבקשת החזר מס הכנסה לשנת המס הנוכחית
                                                </div>
                                            </div>

                                            <div class="limitation-item">
                                                <span class="limitation-icon">⚠️</span>
                                                <div class="limitation-text">
                                                    ייפוי הכוח <strong>אינו כולל</strong> הרשאה לטיפול בנושאי מס אחרים (מע"מ, מס רכוש, וכו')
                                                </div>
                                            </div>

                                            <div class="limitation-item">
                                                <span class="limitation-icon">⚠️</span>
                                                <div class="limitation-text">
                                                    ייפוי הכוח <strong>אינו כולל</strong> הרשאה לחתימה על הסכמים או התחייבויות כספיות
                                                </div>
                                            </div>
                                        </div>

                                        <div class="form-section-title">תוקף ייפוי הכוח</div>

                                        <div class="validity-section">
                                            <div class="validity-item">
                                                <label>תאריך תחילה:</label>
                                                <span class="validity-date" id="poa-startDate">תאריך החתימה</span>
                                            </div>
                                            <div class="validity-item">
                                                <label>תאריך סיום:</label>
                                                <span class="validity-date">31/12/2025 או עם השלמת הטיפול בבקשה</span>
                                            </div>
                                        </div>

                                        <div class="form-footer">
                                            <div class="signature-area">
                                                <div class="signature-field">
                                                    <label>תאריך:</label>
                                                    <span class="signature-line" id="poa-signatureDate">יתמלא בחתימה</span>
                                                </div>
                                                <div class="signature-field">
                                                    <label>חתימת המרשה:</label>
                                                    <span class="signature-line">יתמלא בחתימה דיגיטלית</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="important-note">
                                    <strong>הערה חשובה:</strong> טופס זה יוגש אוטומטית לרשות המסים יחד עם בקשת החזר המס.
                                    ייפוי הכוח יבוטל אוטומטית עם השלמת הטיפול בבקשה.
                                </div>
                            </div>
                        </div>

                        <!-- Privacy Protection -->
                        <div class="terms-section" id="privacy-section">
                            <div class="terms-header">
                                <div class="terms-icon">🔒</div>
                                <h4>הגנת פרטיות ומידע</h4>
                            </div>
                            <div class="terms-content">
                                <ul>
                                    <li><strong>הצפנה מתקדמת</strong> - כל הנתונים מוצפנים ברמה בנקאית</li>
                                    <li><strong>אחסון מאובטח</strong> - בשרתים מוגנים בישראל בלבד</li>
                                    <li><strong>גישה מוגבלת</strong> - רק לצוות מורשה ומאומן</li>
                                    <li><strong>מחיקה אוטומטית</strong> - נתונים נמחקים לאחר 7 שנים</li>
                                    <li><strong>ללא שיתוף</strong> - המידע לא יועבר לצדדים שלישיים</li>
                                </ul>
                            </div>
                        </div>

                        <!-- Regulatory Compliance -->
                        <div class="terms-section" id="regulatory-section">
                            <div class="terms-header">
                                <div class="terms-icon">⚖️</div>
                                <h4>ציות לחוק רגולטורי</h4>
                            </div>
                            <div class="terms-content">
                                <ul>
                                    <li><strong>רישיון עורכי דין</strong> - השירות מופעל על ידי עורכי דין מורשים</li>
                                    <li><strong>ציות לחוק הגנת הפרטיות</strong> - התשע"א 2011</li>
                                    <li><strong>תקנות מס הכנסה</strong> - פעילות לפי כל התקנות הרלוונטיות</li>
                                    <li><strong>ביטוח אחריות מקצועית</strong> - כיסוי מלא לטיפול בתיקים</li>
                                    <li><strong>פיקוח לשכת עורכי הדין</strong> - תחת פיקוח מקצועי מלא</li>
                                </ul>
                            </div>
                        </div>

                        <!-- Client Rights -->
                        <div class="terms-section" id="rights-section">
                            <div class="terms-header">
                                <div class="terms-icon">👤</div>
                                <h4>זכויות הלקוח</h4>
                            </div>
                            <div class="terms-content">
                                <ul>
                                    <li><strong>זכות ביטול</strong> - ניתן לבטל את השירות תוך 14 ימים</li>
                                    <li><strong>זכות עיון</strong> - צפייה במידע האישי בכל עת</li>
                                    <li><strong>זכות תיקון</strong> - עדכון נתונים שגויים</li>
                                    <li><strong>זכות מחיקה</strong> - מחיקת המידע לפי בקשה</li>
                                    <li><strong>תמיכה מקצועית</strong> - זמינות טלפונית ובאימייל</li>
                                    <li><strong>החזר כספי</strong> - במקרה של אי הצלחה בהגשה</li>
                                </ul>
                            </div>
                        </div>

                        <!-- Power of Attorney Section - Full Form 8832 -->
                        <div class="power-of-attorney-final-section">
                            <h4>📜 ייפוי כוח רשמי - טופס 8832</h4>
                            <div class="legal-explanation">
                                <div class="legal-title">⚖️ אישור ייפוי הכוח</div>
                                <div class="legal-content">
                                    <p><strong>בחתימתך למטה, אתה מאשר ומעניק ייפוי כוח רשמי (טופס 8832) ל-TaxFree Pro לטיפול בבקשת החזר המס שלך.</strong></p>
                                    <p><strong>להלן הטופס המלא שיוגש לרשות המסים:</strong></p>
                                </div>
                            </div>

                            <!-- Full Form 8832 Display -->
                            <div class="form-8832-container" id="fullForm8832">
                                <div class="form-header">
                                    <div class="form-logo">
                                        <div class="israel-emblem">🇮🇱</div>
                                        <div class="form-title">
                                            <h3>מדינת ישראל</h3>
                                            <h4>רשות המסים</h4>
                                            <h5>טופס 8832 - ייפוי כוח</h5>
                                        </div>
                                    </div>
                                    <div class="form-number">
                                        <span>מס' טופס: 8832</span>
                                        <span>גרסה: 01/2024</span>
                                    </div>
                                </div>

                                <div class="form-content">
                                    <div class="form-section-title">פרטי הנותן ייפוי הכוח (המרשה)</div>

                                    <div class="form-fields">
                                        <div class="form-field">
                                            <label>שם פרטי ומשפחה:</label>
                                            <div class="field-value" id="poa-fullName-final">יתמלא אוטומטית</div>
                                        </div>
                                        <div class="form-field">
                                            <label>מספר זהות:</label>
                                            <div class="field-value" id="poa-idNumber-final">יתמלא אוטומטית</div>
                                        </div>
                                        <div class="form-field">
                                            <label>כתובת:</label>
                                            <div class="field-value">לפי הרישומים ברשות המסים</div>
                                        </div>
                                        <div class="form-field">
                                            <label>טלפון:</label>
                                            <div class="field-value" id="poa-phone-final">יתמלא אוטומטית</div>
                                        </div>
                                        <div class="form-field">
                                            <label>דואר אלקטרוני:</label>
                                            <div class="field-value" id="poa-email-final">יתמלא אוטומטית</div>
                                        </div>
                                    </div>

                                    <div class="form-section-title">פרטי המקבל ייפוי הכוח (המורשה)</div>

                                    <div class="form-fields">
                                        <div class="form-field">
                                            <label>שם החברה/עורך דין:</label>
                                            <div class="field-value">TaxFree Pro - שירותי מס מתקדמים בע"מ</div>
                                        </div>
                                        <div class="form-field">
                                            <label>מספר רישיון:</label>
                                            <div class="field-value">רישיון עורך דין מס' 12345</div>
                                        </div>
                                        <div class="form-field">
                                            <label>כתובת:</label>
                                            <div class="field-value">רחוב הטכנולוגיה 1, תל אביב</div>
                                        </div>
                                        <div class="form-field">
                                            <label>טלפון:</label>
                                            <div class="field-value">03-1234567</div>
                                        </div>
                                    </div>

                                    <div class="form-section-title">היקף ייפוי הכוח</div>

                                    <div class="authorization-scope">
                                        <p><strong>אני מרשה בזה את המורשה לפעול בשמי ובמקומי בכל הנושאים הבאים:</strong></p>

                                        <div class="scope-items">
                                            <div class="scope-item">
                                                <span class="scope-number">1.</span>
                                                <div class="scope-content">
                                                    <strong>הגשת בקשה להחזר מס הכנסה</strong> - לשנת המס הרלוונטית, כולל מילוי וחתימה על כל הטפסים הנדרשים
                                                </div>
                                            </div>

                                            <div class="scope-item">
                                                <span class="scope-number">2.</span>
                                                <div class="scope-content">
                                                    <strong>קבלת מידע ממס הכנסה</strong> - צפייה בנתונים, קבלת אישורים ומסמכים הנוגעים לבקשת החזר המס
                                                </div>
                                            </div>

                                            <div class="scope-item">
                                                <span class="scope-number">3.</span>
                                                <div class="scope-content">
                                                    <strong>מעקב וטיפול בבקשה</strong> - בדיקת סטטוס, מתן הסברים נוספים, הגשת מסמכים משלימים
                                                </div>
                                            </div>

                                            <div class="scope-item">
                                                <span class="scope-number">4.</span>
                                                <div class="scope-content">
                                                    <strong>תיקון ועדכון נתונים</strong> - במידת הצורך, לצורך השלמת הבקשה או תיקון טעויות
                                                </div>
                                            </div>

                                            <div class="scope-item">
                                                <span class="scope-number">5.</span>
                                                <div class="scope-content">
                                                    <strong>קבלת התכתבויות</strong> - מרשות המסים בנושא בקשת החזר המס, כולל החלטות ודרישות
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-section-title">הגבלות ייפוי הכוח</div>

                                    <div class="limitations">
                                        <div class="limitation-item">
                                            <span class="limitation-icon">⚠️</span>
                                            <div class="limitation-text">
                                                ייפוי כוח זה מוגבל <strong>אך ורק</strong> לטיפול בבקשת החזר מס הכנסה לשנת המס הנוכחית
                                            </div>
                                        </div>

                                        <div class="limitation-item">
                                            <span class="limitation-icon">⚠️</span>
                                            <div class="limitation-text">
                                                ייפוי הכוח <strong>אינו כולל</strong> הרשאה לטיפול בנושאי מס אחרים (מע"מ, מס רכוש, וכו')
                                            </div>
                                        </div>

                                        <div class="limitation-item">
                                            <span class="limitation-icon">⚠️</span>
                                            <div class="limitation-text">
                                                ייפוי הכוח <strong>אינו כולל</strong> הרשאה לחתימה על הסכמים או התחייבויות כספיות
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-section-title">תוקף ייפוי הכוח</div>

                                    <div class="validity-section">
                                        <div class="validity-item">
                                            <label>תאריך תחילה:</label>
                                            <span class="validity-date" id="poa-startDate-final">תאריך החתימה</span>
                                        </div>
                                        <div class="validity-item">
                                            <label>תאריך סיום:</label>
                                            <span class="validity-date">31/12/2025 או עם השלמת הטיפול בבקשה</span>
                                        </div>
                                    </div>

                                    <div class="form-footer">
                                        <div class="signature-area">
                                            <div class="signature-field">
                                                <label>תאריך:</label>
                                                <span class="signature-line" id="poa-signatureDate-final">יתמלא בחתימה</span>
                                            </div>
                                            <div class="signature-field-canvas">
                                                <label>חתימת המרשה:</label>
                                                <div class="form-signature-container">
                                                    <canvas id="formSignatureCanvas" width="400" height="120"></canvas>
                                                    <div class="form-signature-controls">
                                                        <button type="button" id="clearFormSignature" class="clear-form-btn">🗑️ נקה</button>
                                                        <div class="form-signature-status">
                                                            <span id="formSignatureStatus">❌ לא נחתם</span>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="signature-instruction">
                                                    <div class="instruction-title">חתום כאן על טופס 8832 הרשמי</div>
                                                    <div class="instruction-details">
                                                        <span>🖱️ במחשב: לחץ וגרור עם העכבר</span>
                                                        <span>👆 במובייל: גע וגרור עם האצבע</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="important-note">
                                <strong>הערה חשובה:</strong> טופס זה יוגש אוטומטית לרשות המסים יחד עם בקשת החזר המס.
                                ייפוי הכוח יבוטל אוטומטית עם השלמת הטיפול בבקשה.
                            </div>

                            <div class="form-actions">
                                <button class="expand-btn" onclick="printPowerOfAttorney(event)">🖨️ הדפס טופס 8832</button>
                            </div>
                        </div>

                        <!-- Comprehensive Agreement Section -->
                        <div class="comprehensive-agreement-section">
                            <h4>📋 הצהרה והסכמה מקיפה</h4>

                            <div class="legal-explanation">
                                <div class="legal-title">⚖️ הצהרת הלקוח המלאה</div>
                                <div class="legal-content">
                                    <p><strong>בסימון התיבה למטה, אני מצהיר/ה ומאשר/ת בזה כדלקמן:</strong></p>
                                </div>
                            </div>

                            <div class="comprehensive-agreement-content">
                                <div class="agreement-section-detailed">
                                    <h5>🔍 לגבי השירות המקצועי:</h5>
                                    <ul>
                                        <li>קראתי, הבנתי ומאשר/ת את תיאור השירות המקצועי של TaxFree Pro</li>
                                        <li>אני מבין/ה שהשירות כולל ייעוץ מס מקצועי על ידי עורכי דין מורשים</li>
                                        <li>אני מודע/ת לכך שהשירות כפוף לפיקוח לשכת עורכי הדין בישראל</li>
                                        <li>אני מבין/ה את התחייבויות המקצועיות: נאמנות, זהירות, דיווח וסודיות</li>
                                    </ul>
                                </div>

                                <div class="agreement-section-detailed">
                                    <h5>💰 לגבי מבנה התמחור:</h5>
                                    <ul>
                                        <li>אני מסכים/ה למבנה התמחור של 15% מגובה ההחזר (כולל מע"מ)</li>
                                        <li>אני מבין/ה שהתשלום יבוצע רק לאחר קבלת ההחזר בפועל</li>
                                        <li>אני מודע/ת לזמני החזר החוקיים: 45 ימים סטנדרטי, עד 75 ימים במקרים מורכבים</li>
                                        <li>אני מבין/ה שאין תשלום במקרה של אי אישור הבקשה על ידי רשות המסים</li>
                                        <li>אני מודע/ת לזכותי לביטול ההסכם תוך 14 ימים ללא עלות</li>
                                    </ul>
                                </div>

                                <div class="agreement-section-detailed">
                                    <h5>📜 לגבי ייפוי הכוח (טופס 8832):</h5>
                                    <ul>
                                        <li>אני מעניק/ה בזה ייפוי כוח רשמי ל-TaxFree Pro לטיפול בבקשת החזר המס</li>
                                        <li>אני מבין/ה את היקף ההרשאות: הגשה, קבלת מידע, מעקב, תיקונים והתכתבויות</li>
                                        <li>אני מודע/ת להגבלות: מוגבל רק לבקשת החזר המס הנוכחית</li>
                                        <li>אני מבין/ה שייפוי הכוח תקף עד להשלמת הטיפול או עד 31/12/2025</li>
                                        <li>אני מודע/ת לזכותי לבטל את ייפוי הכוח בכל עת בהודעה בכתב</li>
                                    </ul>
                                </div>

                                <div class="agreement-section-detailed">
                                    <h5>🔒 לגבי הגנת פרטיות ועיבוד מידע:</h5>
                                    <ul>
                                        <li>אני מסכים/ה לעיבוד המידע האישי שלי לצורך מתן השירות</li>
                                        <li>אני מבין/ה שהמידע יישמר במערכות מאובטחות בישראל בלבד</li>
                                        <li>אני מודע/ת לזכויותיי: עיון, תיקון, מחיקה והעברת נתונים</li>
                                        <li>אני מבין/ה שהמידע לא יועבר לצדדים שלישיים ללא הסכמתי</li>
                                        <li>אני מודע/ת למחיקה אוטומטית של הנתונים לאחר 7 שנים</li>
                                    </ul>
                                </div>

                                <div class="agreement-section-detailed">
                                    <h5>⚖️ לגבי ציות רגולטורי ומשפטי:</h5>
                                    <ul>
                                        <li>אני מאשר/ת שהשירות ניתן על ידי עורכי דין מורשים ברישיון פעיל</li>
                                        <li>אני מבין/ה שהשירות פועל בהתאם לחוק מס הכנסה וחוק הגנת הפרטיות</li>
                                        <li>אני מודע/ת לכיסוי ביטוח האחריות המקצועית של עד 2 מיליון ₪</li>
                                        <li>אני מבין/ה את זכותי להגיש תלונה ללשכת עורכי הדין במקרה הצורך</li>
                                    </ul>
                                </div>

                                <div class="agreement-section-detailed">
                                    <h5>👤 לגבי זכויות הלקוח:</h5>
                                    <ul>
                                        <li>אני מכיר/ה בזכויותיי המלאות כלקוח בהתאם לחוק הגנת הצרכן</li>
                                        <li>אני מבין/ה את זכותי לקבל תמיכה מקצועית לאורך כל התהליך</li>
                                        <li>אני מודע/ת לזכותי לקבל עדכונים שוטפים על התקדמות הבקשה</li>
                                        <li>אני מבין/ה את זכותי להחזר כספי במקרה של אי הצלחה בהגשה</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="final-agreement-checkbox">
                                <label class="comprehensive-agreement-item">
                                    <input type="checkbox" id="comprehensiveAgreement" required>
                                    <span class="comprehensive-checkmark"></span>
                                    <div class="agreement-text">
                                        <strong>אני מצהיר/ה בזה שקראתי בעיון, הבנתי במלואם ומאשר/ת את כל התנאים, ההסברים המשפטיים וההצהרות המפורטות לעיל.
                                        אני מעניק/ה ייפוי כוח רשמי (טופס 8832) ל-TaxFree Pro לטיפול בבקשת החזר המס שלי בהתאם לכל האמור לעיל,
                                        ואני מסכים/ה לכל תנאי השירות והתמחור כמפורט.</strong>
                                    </div>
                                </label>
                            </div>
                        </div>

                        <!-- Agreement Confirmation -->
                        <div class="agreement-section">
                            <h4>אישור והסכמה</h4>
                            <p><strong>בסימון התיבות למטה, אתה מאשר את ההצהרה המקיפה לעיל ומעניק ייפוי כוח רשמי (טופס 8832) לטיפול בבקשת החזר המס:</strong></p>

                            <div class="agreement-checkboxes">
                                <div class="checkbox-item">
                                    <input type="checkbox" id="agreeTerms" required>
                                    <label for="agreeTerms">
                                        <i class="fas fa-check-circle"></i>
                                        אני מאשר את כל תנאי השימוש והסכמות שקראתי לעיל
                                    </label>
                                </div>

                                <div class="checkbox-item">
                                    <input type="checkbox" id="agreePowerOfAttorney" required>
                                    <label for="agreePowerOfAttorney">
                                        <i class="fas fa-file-contract"></i>
                                        אני מעניק יפוי כח רשמי (טופס 8832) לטיפול בבקשת החזר המס
                                    </label>
                                </div>

                                <div class="checkbox-item">
                                    <input type="checkbox" id="agreeDataProcessing" required>
                                    <label for="agreeDataProcessing">
                                        <i class="fas fa-shield-alt"></i>
                                        אני מסכים לעיבוד הנתונים האישיים שלי לצורך החזר המס
                                    </label>
                                </div>

                                <div class="checkbox-item">
                                    <input type="checkbox" id="confirmAccuracy" required>
                                    <label for="confirmAccuracy">
                                        <i class="fas fa-user-check"></i>
                                        אני מאשר שכל הפרטים שמסרתי נכונים ומדויקים
                                    </label>
                                </div>
                            </div>

                            <div class="agreement-note">
                                <div class="note-icon">
                                    <i class="fas fa-info-circle"></i>
                                </div>
                                <div class="note-text">
                                    <strong>שים לב:</strong> החתימה הדיגיטלית הרשמית תתבצע בטופס 8832 למטה.
                                    סימון כל התיבות לעיל נדרש להמשך התהליך.
                                </div>
                            </div>

                            <div class="legal-explanation">
                                <div class="legal-title">⚖️ משמעות האישור</div>
                                <div class="legal-content">
                                    <p><strong>בהתאם לחוק החתימה האלקטרונית התשס"א-2001:</strong></p>
                                    <ul>
                                        <li>סימון התיבות מהווה אישור מלא לכל התנאים</li>
                                        <li>החתימה הרשמית בטופס 8832 מעניקה ייפוי כח ל-TaxFree Pro</li>
                                        <li>כל הנתונים נשמרים במערכת מאובטחת עם חותמת זמן</li>
                                        <li>ניתן לבטל את ההסכם תוך 14 ימים בהתאם לחוק הגנת הצרכן</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- Final Action Buttons -->
                        <div class="final-actions">
                            <div class="action-summary">
                                <div class="summary-icon">
                                    <i class="fas fa-check-circle"></i>
                                </div>
                                <div class="summary-text">
                                    <h4>מוכן להמשך?</h4>
                                    <p>לאחר החתימה תועבר לחישוב החזר המס המקצועי</p>
                                </div>
                            </div>

                            <div class="action-buttons">
                                <button type="button" class="secondary-btn" onclick="goBackToTerms()">
                                    <i class="fas fa-arrow-right"></i>
                                    חזור לעריכה
                                </button>
                                <button type="button" id="proceedToCalculator" class="proceed-btn" disabled>
                                    <i class="fas fa-calculator"></i>
                                    המשך לחישוב מס
                                    <span class="btn-subtitle">נדרש סימון כל התיבות וחתימה על טופס 8832</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tax Calculator Step (Hidden initially) -->
                <div id="taxCalculatorStep" class="step-container" style="display: none;">
                    <div class="step-header">
                        <div class="step-number">3</div>
                        <h3>🧮 חישוב החזר מס</h3>
                        <p>העלה מסמכים לזיהוי אוטומטי או מלא את הפרטים ידנית</p>
                        <button type="button" class="back-btn" onclick="goBackToTerms()">
                            ← חזור לתנאי השימוש
                        </button>
                    </div>

                <!-- OCR Document Upload Section -->
                <div class="ocr-section">
                    <h3>📄 העלאת מסמכים אוטומטית</h3>
                    <p>העלה את המסמכים שלך והמערכת תקרא אותם אוטומטיט באמצעות OCR</p>

                    <div class="upload-area" id="uploadArea">
                        <div class="upload-content">
                            <div class="upload-icon">📁</div>
                            <h4>גרור קבצים לכאן או לחץ לבחירה</h4>
                            <p>תומך ב: PDF, JPG, PNG, TIFF, BMP, GIF, WebP, HEIC, DOC, DOCX, XLS, XLSX</p>
                            <input type="file" id="fileInput" multiple accept=".pdf,.jpg,.jpeg,.png,.tiff,.tif,.bmp,.gif,.webp,.heic,.heif,.doc,.docx,.xls,.xlsx" style="display: none;">
                            <button type="button" class="upload-btn" onclick="document.getElementById('fileInput').click()">
                                📤 בחר קבצים
                            </button>
                        </div>
                    </div>

                    <div class="document-types">
                        <h4>סוגי מסמכים נתמכים:</h4>
                        <div class="doc-grid">
                            <div class="doc-item">📋 טופס 106 - ריכוז שכר</div>
                            <div class="doc-item">💰 אישור ניכוי מס במקור</div>
                            <div class="doc-item">🏥 אישור דמי אבטלה</div>
                            <div class="doc-item">⚕️ אישור דמי פגיעה בעבודה</div>
                            <div class="doc-item">🎖️ תגמולי מילואים</div>
                            <div class="doc-item">👶 דמי לידה</div>
                            <div class="doc-item">📊 טופס 161 - פרישה/פיטורין</div>
                            <div class="doc-item">📈 טופס 867 - ניירות ערך</div>
                            <div class="doc-item">🏦 אישור קופות גמל</div>
                            <div class="doc-item">🏘️ אישור תושבות בישוב מזכה</div>
                            <div class="doc-item">🎓 טופס 119 - זכאות לתואר</div>
                            <div class="doc-item">♿ טופס 127 - ילד נטול יכולת</div>
                            <div class="doc-item">💝 קבלות תרומות</div>
                        </div>
                    </div>

                    <div id="uploadedFiles" class="uploaded-files"></div>
                    <div id="ocrProgress" class="ocr-progress" style="display: none;">
                        <div class="progress-bar">
                            <div class="progress-fill"></div>
                        </div>
                        <p>מעבד מסמכים... <span id="progressText">0%</span></p>
                    </div>

                    <div class="ocr-results" id="ocrResults" style="display: none;">
                        <h4>📊 נתונים שנמצאו במסמכים:</h4>
                        <div id="extractedData"></div>
                        <button type="button" class="apply-data-btn" onclick="applyExtractedData()">
                            ✅ החל נתונים על הטופס
                        </button>
                    </div>
                </div>

                <div class="divider">
                    <span>או מלא ידנית</span>
                </div>
                
                    <form id="taxForm">
                    <div class="form-grid">
                        <!-- Basic Info -->
                        <div>
                            <h3 class="form-section-title">📋 פרטים בסיסיים</h3>
                            <div class="form-group">
                                <label for="year">שנת מס:</label>
                                <select id="year" required>
                                    <option value="">בחר שנה</option>
                                    <option value="2020">2020</option>
                                    <option value="2021">2021</option>
                                    <option value="2022">2022</option>
                                    <option value="2023">2023</option>
                                    <option value="2024">2024</option>
                                    <option value="2025">2025</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="gender">מין:</label>
                                <select id="gender" required>
                                    <option value="">בחר מין</option>
                                    <option value="male">גבר</option>
                                    <option value="female">אישה</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="income">הכנסה שנתית ברוטו (₪):</label>
                                <input type="number" id="income" placeholder="לדוגמה: 350000" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="taxPaid">מס ששולם בפועל (₪):</label>
                                <input type="number" id="taxPaid" placeholder="לדוגמה: 85000">
                            </div>
                        </div>

                        <!-- Deductions -->
                        <div>
                            <h3 class="form-section-title">💰 ניכויים</h3>
                            <div class="form-group">
                                <label for="donations">תרומות (₪):</label>
                                <input type="number" id="donations" placeholder="עד 35% מההכנסה">
                                <small style="color: #6b7280; font-size: 0.9em;">זיכוי 35% מסכום התרומה, עד 35% מההכנסה החייבת</small>
                            </div>
                            
                            <div class="form-group">
                                <label for="studyFund">קרן השתלמות (₪):</label>
                                <input type="number" id="studyFund" placeholder="תקרה 2025: 21,096 ₪">
                                <small style="color: #6b7280; font-size: 0.9em;">זיכוי 35% עד תקרה של 21,096 ₪</small>
                            </div>
                            
                            <div class="form-group">
                                <label for="foreignTax">מס ששולם בחו"ל (₪):</label>
                                <input type="number" id="foreignTax" placeholder="לזיכוי מס זר">
                            </div>
                        </div>
                    </div>

                    <!-- פטורים והטבות מיוחדות -->
                    <div class="form-section">
                        <h3 style="color: #7c3aed; margin-bottom: 20px;">🎁 פטורים והטבות מיוחדות</h3>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="rentalIncome">הכנסה מהשכרת דירת מגורים (₪):</label>
                                <input type="number" id="rentalIncome" placeholder="פטור מלא עד 5,196 ₪">
                                <small style="color: #6b7280; font-size: 0.9em;">פטור מלא עד 5,196 ₪ + 3 מסלולי מיסוי מועדפים (כולל מס 10% קבוע או מדרגות לפי גיל)</small>
                            </div>

                            <div class="form-group">
                                <label for="scholarships">מלגות לימוד ומחקר (₪):</label>
                                <input type="number" id="scholarships" placeholder="פטור עד 98,000 ₪">
                                <small style="color: #6b7280; font-size: 0.9em;">פטור עד 98,000 ₪ לשנה</small>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="agricultureIncome">הכנסה מחקלאות (₪):</label>
                                <input type="number" id="agricultureIncome" placeholder="פטור עד 70,800 ₪">
                                <small style="color: #6b7280; font-size: 0.9em;">פטור עד 70,800 ₪ ביישובי זכאות לאומית</small>
                            </div>

                            <div class="form-group">
                                <label for="angelInvestment">השקעה לפי חוק האנג'ל (₪):</label>
                                <input type="number" id="angelInvestment" placeholder="זיכוי 25% עד 5M ₪">
                                <small style="color: #6b7280; font-size: 0.9em;">זיכוי 25% מההשקעה עד 5 מיליון ₪</small>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="artistIncome">הכנסה כאמן/יוצר (₪):</label>
                                <input type="number" id="artistIncome" placeholder="פטור 50% עד 300K ₪">
                                <small style="color: #6b7280; font-size: 0.9em;">פטור 50% מההכנסה עד 300,000 ₪</small>
                            </div>

                            <div class="form-group">
                                <label for="disability">אחוז נכות רפואית:</label>
                                <select id="disability">
                                    <option value="0">ללא נכות</option>
                                    <option value="50">50% נכות</option>
                                    <option value="75">75% נכות</option>
                                    <option value="90">90% נכות ומעלה</option>
                                    <option value="100">100% נכות</option>
                                </select>
                                <small style="color: #6b7280; font-size: 0.9em;">נכות 90%+ זכאית לפטורים ונקודות זיכוי נוספות</small>
                            </div>
                        </div>

                        <!-- תיבות סימון להטבות -->
                        <div class="checkbox-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; margin-top: 20px;">
                            <label class="checkbox-container">
                                <input type="checkbox" id="isQualifiedSettlement">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">מתגורר ביישוב זכאות לאומית</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="isArtist">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">אמן/יוצר מוכר</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="isSecurityZone">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">תושב אזור ביטחון (עוטף עזה)</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="isNewImmigrant">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">עולה חדש</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="isReturningResident">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">תושב חוזר</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="isDischargedSoldier">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">חייל משוחרר (עד שנתיים)</span>
                            </label>
                        </div>

                        <!-- הכנסות נוספות ופטורים -->
                        <div class="form-row">
                            <div class="form-group">
                                <label for="socialSecurityBenefits">קצבאות ביטוח לאומי (₪):</label>
                                <input type="number" id="socialSecurityBenefits" placeholder="זקנה, נכות, שארים, ילדים">
                                <small style="color: #6b7280; font-size: 0.9em;">פטור מלא (למעט קצבת פרישה)</small>
                            </div>

                            <div class="form-group">
                                <label for="pensionBenefits">פנסיות ותגמולים (₪):</label>
                                <input type="number" id="pensionBenefits" placeholder="פנסיה תקציבית, פיצויים">
                                <small style="color: #6b7280; font-size: 0.9em;">פטור על פנסיה תקציבית ופיצויים</small>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="capitalLosses">הפסדים בשוק ההון (₪):</label>
                                <input type="number" id="capitalLosses" placeholder="הפסדי רווח הון">
                                <small style="color: #6b7280; font-size: 0.9em;">קיזוז מול רווחי הון למשך 5 שנים</small>
                            </div>

                            <div class="form-group">
                                <label for="workInjuryCompensation">פיצויי נפגעי עבודה/תאונה (₪):</label>
                                <input type="number" id="workInjuryCompensation" placeholder="פיצויים לנפגעי עבודה">
                                <small style="color: #6b7280; font-size: 0.9em;">פטור מלא ממס</small>
                            </div>
                        </div>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="reserveDutyPay">דמי מילואים (₪):</label>
                                <input type="number" id="reserveDutyPay" placeholder="דמי מילואים">
                                <small style="color: #6b7280; font-size: 0.9em;">פטור מלא על דמי מילואים</small>
                            </div>

                            <div class="form-group">
                                <label for="employeeOptions">הכנסה מאופציות עובדים (₪):</label>
                                <input type="number" id="employeeOptions" placeholder="מימוש אופציות">
                                <small style="color: #6b7280; font-size: 0.9em;">מסלול רווח הון 25% (במקום עד 50%)</small>
                            </div>
                        </div>

                        <!-- פטורים ממסים על נדל"ן -->
                        <div class="form-section">
                            <h4 style="color: #7c3aed; margin: 20px 0 15px 0;">🏠 פטורים ממסים על נדל"ן</h4>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="propertyPurchaseValue">שווי דירה לרכישה (₪):</label>
                                    <input type="number" id="propertyPurchaseValue" placeholder="שווי הדירה">
                                    <small style="color: #6b7280; font-size: 0.9em;">פטור מלא עד 1.98M ₪ לרוכש ראשון</small>
                                </div>

                                <div class="form-group">
                                    <label for="propertySaleValue">שווי דירה למכירה (₪):</label>
                                    <input type="number" id="propertySaleValue" placeholder="שווי דירה נמכרת">
                                    <small style="color: #6b7280; font-size: 0.9em;">פטור ממס שבח לדירה יחידה</small>
                                </div>
                            </div>

                            <div class="checkbox-grid" style="margin-top: 15px;">
                                <label class="checkbox-container">
                                    <input type="checkbox" id="isFirstHomeBuyer">
                                    <span class="checkmark"></span>
                                    <span class="checkbox-text">רוכש דירה ראשון</span>
                                </label>

                                <label class="checkbox-container">
                                    <input type="checkbox" id="isHousingUpgrader">
                                    <span class="checkmark"></span>
                                    <span class="checkbox-text">משפר דיור</span>
                                </label>

                                <label class="checkbox-container">
                                    <input type="checkbox" id="isInheritanceProperty">
                                    <span class="checkmark"></span>
                                    <span class="checkbox-text">דירת ירושה</span>
                                </label>

                                <label class="checkbox-container">
                                    <input type="checkbox" id="isGiftProperty">
                                    <span class="checkmark"></span>
                                    <span class="checkbox-text">מתנה בין קרובי משפחה</span>
                                </label>
                            </div>
                        </div>

                        <!-- פטורים עסקיים ומע"מ -->
                        <div class="form-section">
                            <h4 style="color: #7c3aed; margin: 20px 0 15px 0;">💼 פטורים עסקיים ומע"מ</h4>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="businessTurnover">מחזור עסקי שנתי (₪):</label>
                                    <input type="number" id="businessTurnover" placeholder="מחזור שנתי">
                                    <small style="color: #6b7280; font-size: 0.9em;">פטור ממע"מ עד 102,000 ₪</small>
                                </div>

                                <div class="form-group">
                                    <label for="personalImportValue">יבוא אישי (USD):</label>
                                    <input type="number" id="personalImportValue" placeholder="שווי יבוא אישי">
                                    <small style="color: #6b7280; font-size: 0.9em;">פטור ממכס ומע"מ עד 75$</small>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="pensionFundDeposits">הפקדות קרן פנסיה (₪):</label>
                                    <input type="number" id="pensionFundDeposits" placeholder="הפקדות שנתיות">
                                    <small style="color: #6b7280; font-size: 0.9em;">פטור עד 18,960 ₪ (עצמאים) / 18,854 ₪ (שכירים)</small>
                                </div>

                                <div class="form-group">
                                    <label for="providentFundDeposits">הפקדות קופת גמל (₪):</label>
                                    <input type="number" id="providentFundDeposits" placeholder="הפקדות קופת גמל">
                                    <small style="color: #6b7280; font-size: 0.9em;">פטור עד תקרה של 70,000 ₪ בשנה</small>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="managerInsurance">ביטוח מנהלים (₪):</label>
                                    <input type="number" id="managerInsurance" placeholder="דמי ביטוח מנהלים">
                                    <small style="color: #6b7280; font-size: 0.9em;">פטור עד 16% או 300,000 ₪</small>
                                </div>

                                <div class="form-group">
                                    <label for="companyLiquidationBenefits">הטבות פירוק חברה (₪):</label>
                                    <input type="number" id="companyLiquidationBenefits" placeholder="רווחים מפירוק">
                                    <small style="color: #6b7280; font-size: 0.9em;">מס דיבידנד מופחת 10% (2025)</small>
                                </div>
                            </div>
                        </div>

                        <!-- פטורים נוספים שחסרו מהטבלה המקורית -->
                        <div class="form-section">
                            <h4 style="color: #7c3aed; margin: 20px 0 15px 0;">📋 פטורים נוספים מהטבלה</h4>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="studyFundWithdrawal">משיכה מקרן השתלמות (₪):</label>
                                    <input type="number" id="studyFundWithdrawal" placeholder="משיכה אחרי 6 שנים">
                                    <small style="color: #6b7280; font-size: 0.9em;">פטור מלא במשיכה אחרי 6 שנים</small>
                                </div>

                                <div class="form-group">
                                    <label for="peripherySettlementBonus">תוספת נקודות זיכוי פריפריה:</label>
                                    <select id="peripherySettlementBonus">
                                        <option value="0">לא תושב פריפריה</option>
                                        <option value="0.5">0.5 נקודות זיכוי</option>
                                        <option value="1">1 נקודת זיכוי</option>
                                        <option value="1.5">1.5 נקודות זיכוי</option>
                                        <option value="2">2 נקודות זיכוי</option>
                                    </select>
                                    <small style="color: #6b7280; font-size: 0.9em;">תוספת נקודות זיכוי לישובים מוכרים</small>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="foreignDividends">דיבידנדים מחו"ל (₪):</label>
                                    <input type="number" id="foreignDividends" placeholder="דיבידנדים מחו״ל">
                                    <small style="color: #6b7280; font-size: 0.9em;">טיפול לפי אמנות מס (גרמניה - פטור מלא)</small>
                                </div>

                                <div class="form-group">
                                    <label for="foreignRoyalties">תמלוגים מחו"ל (₪):</label>
                                    <input type="number" id="foreignRoyalties" placeholder="תמלוגים מחו״ל">
                                    <small style="color: #6b7280; font-size: 0.9em;">חייב במס עם זיכוי על מס ששולם בחו"ל</small>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="foreignInterest">ריבית מחו"ל (₪):</label>
                                    <input type="number" id="foreignInterest" placeholder="ריבית מחו״ל">
                                    <small style="color: #6b7280; font-size: 0.9em;">חייב במס עם זיכוי על מס ששולם בחו"ל</small>
                                </div>

                                <div class="form-group">
                                    <label for="developmentAreaInvestmentAmount">השקעה באזור תע"ן (₪):</label>
                                    <input type="number" id="developmentAreaInvestmentAmount" placeholder="סכום השקעה">
                                    <small style="color: #6b7280; font-size: 0.9em;">זיכוי השקעה 30% מסכום ההשקעה</small>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="studentsOver18">ילדים סטודנטים מעל 18 (מספר):</label>
                                    <input type="number" id="studentsOver18" placeholder="מספר ילדים סטודנטים">
                                    <small style="color: #6b7280; font-size: 0.9em;">2 נקודות זיכוי לכל ילד סטודנט מעל 18</small>
                                </div>

                                <div class="form-group">
                                    <label for="doctorSpecialCredit">זיכוי מיוחד לרופאים (₪):</label>
                                    <input type="number" id="doctorSpecialCredit" placeholder="עד 18,000 ₪">
                                    <small style="color: #6b7280; font-size: 0.9em;">זיכוי שנתי עד 18,000 ₪ לרופאים במערכת ציבורית</small>
                                </div>
                            </div>

                            <!-- פטורים נוספים שחסרו מהטבלה המקורית -->
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="militaryServiceType">סוג שירות צבאי:</label>
                                    <select id="militaryServiceType">
                                        <option value="none">ללא שירות צבאי</option>
                                        <option value="full">שירות צבאי מלא</option>
                                        <option value="partial">שירות צבאי חלקי</option>
                                        <option value="combat">שירות קרבי</option>
                                        <option value="officer">קצין</option>
                                    </select>
                                    <small style="color: #6b7280; font-size: 0.9em;">זיכוי נקודות לפי סוג השירות הצבאי</small>
                                </div>

                                <div class="form-group">
                                    <label for="academicDegrees">תארים אקדמיים:</label>
                                    <select id="academicDegrees" multiple>
                                        <option value="bachelor">תואר ראשון</option>
                                        <option value="master">תואר שני</option>
                                        <option value="doctorate">תואר שלישי</option>
                                        <option value="professional">תואר מקצועי</option>
                                    </select>
                                    <small style="color: #6b7280; font-size: 0.9em;">זיכוי נקודות לכל תואר אקדמי</small>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="newImmigrantYears">שנות עלייה/חזרה:</label>
                                    <select id="newImmigrantYears">
                                        <option value="none">לא עולה חדש/תושב חוזר</option>
                                        <option value="0-18">0-18 חודשים</option>
                                        <option value="19-30">19-30 חודשים</option>
                                        <option value="31-42">31-42 חודשים</option>
                                        <option value="over-42">מעל 42 חודשים</option>
                                    </select>
                                    <small style="color: #6b7280; font-size: 0.9em;">זיכוי נקודות לפי תקופת העלייה/חזרה</small>
                                </div>

                                <div class="form-group">
                                    <label for="singleParentChildren">ילדים להורה יחיד (מספר):</label>
                                    <input type="number" id="singleParentChildren" placeholder="מספר ילדים">
                                    <small style="color: #6b7280; font-size: 0.9em;">זיכוי נוסף להורה יחיד</small>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="disabledChildren">ילדים עם נכות (מספר):</label>
                                    <input type="number" id="disabledChildren" placeholder="מספר ילדים עם נכות">
                                    <small style="color: #6b7280; font-size: 0.9em;">זיכוי נוסף לילדים עם נכות</small>
                                </div>

                                <div class="form-group">
                                    <label for="workingWomanBonus">בונוס אישה עובדת (₪):</label>
                                    <input type="number" id="workingWomanBonus" placeholder="בונוס שנתי">
                                    <small style="color: #6b7280; font-size: 0.9em;">בונוס נוסף לאישה עובדת מעבר לנקודות הזיכוי</small>
                                </div>
                            </div>

                            <!-- זיכויים בסיסיים שחסרו מהטבלה -->
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="childrenUnder6">ילדים עד גיל 6 (מספר):</label>
                                    <input type="number" id="childrenUnder6" placeholder="מספר ילדים עד 6">
                                    <small style="color: #6b7280; font-size: 0.9em;">3.5 נקודות זיכוי לכל ילד עד גיל 6</small>
                                </div>

                                <div class="form-group">
                                    <label for="children6to18">ילדים גיל 6-18 (מספר):</label>
                                    <input type="number" id="children6to18" placeholder="מספר ילדים 6-18">
                                    <small style="color: #6b7280; font-size: 0.9em;">2.5 נקודות זיכוי לכל ילד גיל 6-18</small>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="mortgageInterestRental">ריבית משכנתא דירה להשכרה (₪):</label>
                                    <input type="number" id="mortgageInterestRental" placeholder="ריבית שנתית">
                                    <small style="color: #6b7280; font-size: 0.9em;">ניכוי ריבית משכנתא גם לדירה להשכרה</small>
                                </div>

                                <div class="form-group">
                                    <label for="basicCreditPoints">נקודות זיכוי בסיסיות:</label>
                                    <input type="number" id="basicCreditPoints" value="2.25" readonly>
                                    <small style="color: #6b7280; font-size: 0.9em;">זיכוי בסיסי לכל אזרח (2.25 נקודות)</small>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="studyFundDeposits">הפקדות קרן השתלמות (₪):</label>
                                    <input type="number" id="studyFundDeposits" placeholder="הפקדות שנתיות">
                                    <small style="color: #6b7280; font-size: 0.9em;">זיכוי 35% מהסכום המופרש, תקרה 21,096 ₪</small>
                                </div>

                                <div class="form-group">
                                    <label for="donationsCarryforward">עודף תרומות משנים קודמות (₪):</label>
                                    <input type="number" id="donationsCarryforward" placeholder="עודף מ-3 שנים אחרונות">
                                    <small style="color: #6b7280; font-size: 0.9em;">ניתן להעביר עודף תרומות עד 3 שנים</small>
                                </div>
                            </div>
                        </div>

                        <!-- תיבות סימון נוספות -->
                        <div class="checkbox-grid" style="margin-top: 20px;">
                            <label class="checkbox-container">
                                <input type="checkbox" id="isSmallBusiness">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">עוסק פטור ממע"מ</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="isFinancialServices">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">שירותים פיננסיים</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="isReturningResidentVeteran">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">תושב חוזר ותיק (10+ שנים)</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="hasHighCapitalIncome">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">הכנסות הוניות מעל 721,560 ₪</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="isCompanyLiquidation2025">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">מפרק חברה ב-2025</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="hasEmployeeOptions">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">בעל אופציות עובדים</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="hasGermanTaxTreaty">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">הכנסות מגרמניה (אמנת מס)</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="isPeripheryResident">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">תושב יישוב פריפריה מוכר</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="hasStudyFundOver6Years">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">קרן השתלמות מעל 6 שנים</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="isPublicHealthDoctor">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">רופא במערכת בריאות ציבורית</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="hasDevelopmentAreaInvestment">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">השקעה באזור תע"ן</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="isWorkingWoman">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">אישה עובדת (1.5 נקודות זיכוי)</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="isSingleParent">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">הורה יחיד</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="hasSpouseNoIncome">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">בן/בת זוג ללא הכנסה</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="hasDisabledChildren">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">יש ילדים עם נכות</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="hasCombatService">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">שירות קרבי</span>
                            </label>

                            <label class="checkbox-container">
                                <input type="checkbox" id="isOfficer">
                                <span class="checkmark"></span>
                                <span class="checkbox-text">קצין לשעבר</span>
                            </label>
                        </div>

                        <div class="info-box" style="background: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 8px; padding: 15px; margin-top: 20px;">
                            <h4 style="color: #0369a1; margin: 0 0 10px 0;">💡 הסבר על כל הפטורים וההטבות</h4>
                            <ul style="margin: 0; padding-right: 20px; color: #374151; font-size: 0.9em;">
                                <li><strong>אזור ביטחון:</strong> זיכוי 20% מההכנסה עד 246,120 ₪</li>
                                <li><strong>נכות 90%+:</strong> פטורים מדורגים + 2.25 נקודות זיכוי נוספות</li>
                                <li><strong>עולה חדש:</strong> פטור מלא על הכנסות מחו"ל</li>
                                <li><strong>תושב חוזר ותיק:</strong> פטור על כל ההכנסות מחו"ל (10+ שנים)</li>
                                <li><strong>חייל משוחרר:</strong> נקודת זיכוי אחת נוספת לשנתיים</li>
                                <li><strong>רוכש ראשון:</strong> פטור ממס רכישה עד 1.98M ₪</li>
                                <li><strong>אופציות עובדים:</strong> מסלול רווח הון 25% במקום מס שולי</li>
                                <li><strong>פירוק חברה 2025:</strong> מס דיבידנד מופחת 10%</li>
                                <li><strong>מס היסף נדל"ן:</strong> פטור על דירות עד 5.38M ₪</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Children -->
                    <div>
                        <h3 class="form-section-title">👶 ילדים</h3>
                        <div class="form-grid">
                            <div class="form-group">
                                <label for="children6to18">ילדים בגיל 6-18:</label>
                                <input type="number" id="children6to18" min="0" value="0">
                            </div>
                            <div class="form-group">
                                <label for="childrenUnder6">ילדים מתחת לגיל 6:</label>
                                <input type="number" id="childrenUnder6" min="0" value="0">
                            </div>
                        </div>
                    </div>

                    <!-- Rights and Benefits -->
                    <div>
                        <h3 class="form-section-title">🎯 זכויות והטבות</h3>
                        <div class="checkbox-grid">
                            <div class="checkbox-item">
                                <label>תואר ראשון</label>
                                <input type="checkbox" id="degree1">
                            </div>
                            <div class="checkbox-item">
                                <label>תואר שני</label>
                                <input type="checkbox" id="degree2">
                            </div>
                            <div class="checkbox-item">
                                <label>תואר שלישי</label>
                                <input type="checkbox" id="degree3">
                            </div>
                            <div class="checkbox-item">
                                <label>חייל משוחרר - שירות מלא</label>
                                <input type="checkbox" id="militaryFull">
                            </div>
                            <div class="checkbox-item">
                                <label>חייל משוחרר - שירות חלקי</label>
                                <input type="checkbox" id="militaryPartial">
                            </div>
                            <div class="checkbox-item">
                                <label>הורה יחידני</label>
                                <input type="checkbox" id="singleParent">
                            </div>
                            <div class="checkbox-item">
                                <label>בן/בת זוג ללא הכנסה</label>
                                <input type="checkbox" id="spouseNoIncome">
                            </div>
                            <div class="checkbox-item">
                                <label>עולה חדש (0-18 חודשים)</label>
                                <input type="checkbox" id="newImmigrant0to18">
                            </div>
                            <div class="checkbox-item">
                                <label>עולה חדש (19-30 חודשים)</label>
                                <input type="checkbox" id="newImmigrant19to30">
                            </div>
                            <div class="checkbox-item">
                                <label>עולה חדש (31-42 חודשים)</label>
                                <input type="checkbox" id="newImmigrant31to42">
                            </div>
                            <div class="checkbox-item">
                                <label>הורה לילד עם מוגבלות</label>
                                <input type="checkbox" id="disabledChild">
                            </div>
                        </div>
                    </div>

                    <button type="submit" class="calculate-btn">🧮 חשב החזר מס</button>
                    </form>

                    <!-- Results -->
                    <div id="result" class="result-section">
                        <h3>📊 תוצאות החישוב</h3>
                        <div id="resultContent"></div>
                    </div>
                </div>
            </div>
        </div>
    </section>



    <!-- PDF.js for PDF processing - Latest version -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/4.0.379/pdf.min.js"></script>

    <!-- HEIC2any for HEIC/HEIF conversion - Latest version -->
    <script src="https://cdn.jsdelivr.net/npm/heic2any@0.0.4/dist/heic2any.min.js"></script>

    <!-- Tesseract.js for OCR - Latest version with better Hebrew support -->
    <script src="https://unpkg.com/tesseract.js@5.0.4/dist/tesseract.min.js"></script>

    <!-- SheetJS for Excel processing -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

    <!-- Mammoth.js for Word processing -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mammoth/1.6.0/mammoth.browser.min.js"></script>

    <!-- Help Modal -->
    <div id="helpModal" class="modal" style="display: none;" aria-hidden="true" role="dialog" aria-labelledby="helpModalTitle">
        <div class="modal-content">
            <div class="modal-header">
                <h2 id="helpModalTitle">
                    <i class="fas fa-question-circle"></i>
                    עזרה ותמיכה
                </h2>
                <button class="close-btn" onclick="closeHelp()" aria-label="סגור">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="modal-body">
                <div class="help-tabs" role="tablist">
                    <button class="tab-btn active" onclick="showHelpTab('guide')" role="tab" aria-selected="true">
                        <i class="fas fa-book"></i>
                        מדריך שימוש
                    </button>
                    <button class="tab-btn" onclick="showHelpTab('faq')" role="tab" aria-selected="false">
                        <i class="fas fa-question"></i>
                        שאלות נפוצות
                    </button>
                    <button class="tab-btn" onclick="showHelpTab('contact')" role="tab" aria-selected="false">
                        <i class="fas fa-phone"></i>
                        צור קשר
                    </button>
                    <button class="tab-btn" onclick="showHelpTab('accessibility')" role="tab" aria-selected="false">
                        <i class="fas fa-universal-access"></i>
                        נגישות
                    </button>
                </div>

                <div id="guide-tab" class="tab-content active" role="tabpanel">
                    <h3>מדריך שימוש במערכת</h3>
                    <div class="help-steps">
                        <div class="help-step">
                            <div class="step-number">1</div>
                            <div class="step-content">
                                <h4>קריאת תנאי השימוש</h4>
                                <p>קרא בעיון את כל סעיפי תנאי השימוש. כל סעיף שנקרא יסומן כהושלם ופס ההתקדמות יתעדכן.</p>
                            </div>
                        </div>
                        <div class="help-step">
                            <div class="step-number">2</div>
                            <div class="step-content">
                                <h4>טופס 8832 - יפוי כח</h4>
                                <p>טופס זה מאפשר לנו לטפל בבקשת החזר המס שלך ברשות המסים. הטופס מאושר ורשמי.</p>
                            </div>
                        </div>
                        <div class="help-step">
                            <div class="step-number">3</div>
                            <div class="step-content">
                                <h4>חתימה דיגיטלית מאובטחת</h4>
                                <p>חתום על הטופס באמצעות החתימה הדיגיטלית המאובטחת. החתימה נשמרת עם חותמת זמן.</p>
                            </div>
                        </div>
                        <div class="help-step">
                            <div class="step-number">4</div>
                            <div class="step-content">
                                <h4>מעבר לחישוב מס</h4>
                                <p>לאחר השלמת כל הסעיפים והחתימה, תוכל להמשיך לחישוב החזר המס המקצועי.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="faq-tab" class="tab-content" role="tabpanel">
                    <h3>שאלות נפוצות</h3>
                    <div class="faq-list">
                        <div class="faq-item">
                            <button class="faq-question" onclick="toggleFaq(this)" aria-expanded="false">
                                <span>מה זה טופס 8832?</span>
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            <div class="faq-answer">
                                <p>טופס 8832 הוא יפוי כח רשמי לרשות המסים המאפשר לנו לטפל בבקשת החזר המס שלך. זהו טופס סטנדרטי ומאושר על ידי רשות המסים הישראלית.</p>
                            </div>
                        </div>
                        <div class="faq-item">
                            <button class="faq-question" onclick="toggleFaq(this)" aria-expanded="false">
                                <span>האם החתימה הדיגיטלית בטוחה?</span>
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            <div class="faq-answer">
                                <p>כן, החתימה הדיגיטלית מוצפנת ומאובטחת בהתאם לתקנים הבינלאומיים ולחוק החתימה האלקטרונית הישראלי. כל חתימה נשמרת עם חותמת זמן ומידע מזהה.</p>
                            </div>
                        </div>
                        <div class="faq-item">
                            <button class="faq-question" onclick="toggleFaq(this)" aria-expanded="false">
                                <span>כמה זמן לוקח התהליך?</span>
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            <div class="faq-answer">
                                <p>קריאת תנאי השימוש וחתימה על הטופס אורכים כ-10-15 דקות. חישוב החזר המס נוסף כ-5-10 דקות. התהליך כולו פשוט ומהיר.</p>
                            </div>
                        </div>
                        <div class="faq-item">
                            <button class="faq-question" onclick="toggleFaq(this)" aria-expanded="false">
                                <span>מה קורה אחרי החתימה?</span>
                                <i class="fas fa-chevron-down"></i>
                            </button>
                            <div class="faq-answer">
                                <p>לאחר החתימה תועבר לשלב חישוב החזר המס, שם תוכל להזין את הנתונים הפיננסיים שלך ולקבל חישוב מדויק של החזר המס הצפוי.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="contact-tab" class="tab-content" role="tabpanel">
                    <h3>צור קשר</h3>
                    <div class="contact-info">
                        <div class="contact-item">
                            <i class="fas fa-phone"></i>
                            <div>
                                <strong>טלפון:</strong>
                                <p>03-1234567 (ימים א'-ה', 9:00-18:00)</p>
                            </div>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-envelope"></i>
                            <div>
                                <strong>אימייל:</strong>
                                <p><EMAIL></p>
                            </div>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-comments"></i>
                            <div>
                                <strong>צ'אט חי:</strong>
                                <p>זמין 24/7 באתר</p>
                            </div>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-map-marker-alt"></i>
                            <div>
                                <strong>כתובת:</strong>
                                <p>רחוב הרצל 123, תל אביב-יפו</p>
                            </div>
                        </div>
                        <div class="contact-item">
                            <i class="fas fa-clock"></i>
                            <div>
                                <strong>שעות פעילות:</strong>
                                <p>א'-ה': 9:00-18:00<br>ו': 9:00-13:00<br>ש': סגור</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="accessibility-tab" class="tab-content" role="tabpanel">
                    <h3>תכונות נגישות</h3>
                    <div class="accessibility-features">
                        <div class="feature-item">
                            <i class="fas fa-moon"></i>
                            <div>
                                <strong>מצב לילה:</strong>
                                <p>החלף לצבעים כהים לקריאה נוחה יותר בתאורה חלשה</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-text-height"></i>
                            <div>
                                <strong>גופן גדול:</strong>
                                <p>הגדל את גודל הטקסט לקריאה טובה יותר</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-adjust"></i>
                            <div>
                                <strong>ניגודיות גבוהה:</strong>
                                <p>הגבר את הניגודיות לראייה טובה יותר</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-book-open"></i>
                            <div>
                                <strong>מצב קריאה:</strong>
                                <p>מצב מיוחד לקריאה ממושכת ונוחה עם רקע בהיר</p>
                            </div>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-keyboard"></i>
                            <div>
                                <strong>קיצורי מקלדת:</strong>
                                <p>Alt+H - פתח עזרה | Alt+T - החלף נושא | ESC - סגור חלונות</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button class="btn-secondary" onclick="closeHelp()">
                    <i class="fas fa-times"></i>
                    סגור
                </button>
                <button class="btn-primary" onclick="startLiveChat()">
                    <i class="fas fa-comments"></i>
                    צ'אט חי
                </button>
            </div>
        </div>
    </div>

    <script src="calculator.js"></script>
    <script src="calculator_updated.js"></script>

    <script>
        // Load personal data from previous step
        function loadPersonalData() {
            const savedData = localStorage.getItem('taxfree_personal_data');
            if (savedData) {
                try {
                    const data = JSON.parse(savedData);
                    console.log('Loading personal data:', data);

                    // Update client information in the form if elements exist
                    const clientFullName = document.getElementById('clientFullName');
                    const clientIdNumber = document.getElementById('clientIdNumber');
                    const clientPhone = document.getElementById('clientPhone');
                    const clientEmail = document.getElementById('clientEmail');

                    if (clientFullName) clientFullName.textContent = data.fullName || '[שם לא זמין]';
                    if (clientIdNumber) clientIdNumber.textContent = data.idNumber || '[ת.ז לא זמינה]';
                    if (clientPhone) clientPhone.textContent = data.phone || '[טלפון לא זמין]';
                    if (clientEmail) clientEmail.textContent = data.email || '[אימייל לא זמין]';

                    // Update signature details if elements exist
                    const signerName = document.getElementById('signerName');
                    if (signerName) signerName.textContent = data.fullName || '-';

                    // Update power of attorney form if elements exist
                    const poaClientName = document.getElementById('poa-clientName');
                    const poaClientId = document.getElementById('poa-clientId');
                    const poaClientPhone = document.getElementById('poa-clientPhone');
                    const poaClientEmail = document.getElementById('poa-clientEmail');

                    if (poaClientName) poaClientName.textContent = data.fullName || '[שם לא זמין]';
                    if (poaClientId) poaClientId.textContent = data.idNumber || '[ת.ז לא זמינה]';
                    if (poaClientPhone) poaClientPhone.textContent = data.phone || '[טלפון לא זמין]';
                    if (poaClientEmail) poaClientEmail.textContent = data.email || '[אימייל לא זמין]';

                    // Show welcome message
                    showWelcomeMessage(data.fullName);

                } catch (error) {
                    console.error('Error loading personal data:', error);
                }
            } else {
                console.log('No personal data found in localStorage');
                showNoDataMessage();
            }
        }

        function showWelcomeMessage(name) {
            // Create and show a welcome message
            const welcomeDiv = document.createElement('div');
            welcomeDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #28a745, #20c997);
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 1000;
                font-weight: bold;
                animation: slideIn 0.5s ease-out;
            `;
            welcomeDiv.innerHTML = `
                <i class="fas fa-user-check" style="margin-left: 8px;"></i>
                שלום ${name}! ברוך הבא לעמוד תנאי השימוש
            `;

            document.body.appendChild(welcomeDiv);

            // Remove after 5 seconds
            setTimeout(() => {
                welcomeDiv.remove();
            }, 5000);
        }

        function showNoDataMessage() {
            // Show message if no data found
            const messageDiv = document.createElement('div');
            messageDiv.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #ffc107, #fd7e14);
                color: white;
                padding: 15px 20px;
                border-radius: 10px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 1000;
                font-weight: bold;
            `;
            messageDiv.innerHTML = `
                <i class="fas fa-exclamation-triangle" style="margin-left: 8px;"></i>
                לא נמצאו פרטים אישיים. אנא מלא תחילה את הפרטים האישיים.
                <br>
                <button onclick="window.location.href='taxfree_pro_complete_website.html'"
                        style="background: white; color: #ffc107; border: none; padding: 5px 10px; border-radius: 5px; margin-top: 10px; cursor: pointer;">
                    חזור לפרטים אישיים
                </button>
            `;

            document.body.appendChild(messageDiv);
        }

        // Add CSS animation for welcome message
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
        `;
        document.head.appendChild(style);

        // Function to go back to personal details page
        function goBackToTerms() {
            if (confirm('האם ברצונך לחזור לעמוד הפרטים האישיים?\n\nהנתונים שמולאו כאן יישמרו.')) {
                window.location.href = 'taxfree_pro_complete_website.html';
            }
        }

        // Progress tracking for terms
        let completedSections = new Set();
        const totalSections = 6; // Total number of terms sections

        function updateTermsProgress() {
            const progressFill = document.getElementById('termsProgress');
            const progressText = document.getElementById('progressText');

            if (progressFill && progressText) {
                const percentage = (completedSections.size / totalSections) * 100;
                progressFill.style.width = percentage + '%';
                progressText.textContent = `התקדמות: ${completedSections.size} מתוך ${totalSections} סעיפים הושלמו`;

                // Enable proceed button if all sections completed
                const proceedBtn = document.getElementById('proceedToCalculator');
                if (proceedBtn) {
                    if (completedSections.size === totalSections) {
                        proceedBtn.disabled = false;
                        proceedBtn.innerHTML = `
                            <i class="fas fa-calculator"></i>
                            המשך לחישוב מס
                            <span class="btn-subtitle">כל הסעיפים הושלמו ✓</span>
                        `;
                    } else {
                        proceedBtn.disabled = true;
                        proceedBtn.innerHTML = `
                            <i class="fas fa-calculator"></i>
                            המשך לחישוב מס
                            <span class="btn-subtitle">נותרו ${totalSections - completedSections.size} סעיפים</span>
                        `;
                    }
                }
            }
        }

        // Mark section as completed
        function markSectionCompleted(sectionId) {
            completedSections.add(sectionId);
            updateTermsProgress();

            // Add visual feedback
            const section = document.getElementById(sectionId);
            if (section) {
                section.style.borderLeft = '6px solid #28a745';
                section.style.background = 'linear-gradient(135deg, #f8fff8 0%, #ffffff 100%)';
            }
        }

        // Enhanced client welcome update
        function updateClientWelcome(data) {
            const clientWelcome = document.getElementById('clientWelcome');
            if (clientWelcome && data) {
                clientWelcome.innerHTML = `
                    <i class="fas fa-user-circle"></i>
                    <span>שלום ${data.fullName} | ת.ז: ${data.idNumber}</span>
                `;
                clientWelcome.style.background = 'linear-gradient(135deg, #e8f5e8, #f0f9ff)';
                clientWelcome.style.border = '2px solid #28a745';
            }
        }

        // Enhanced welcome message
        function showWelcomeMessage(name) {
            const welcomeDiv = document.createElement('div');
            welcomeDiv.style.cssText = `
                position: fixed;
                top: 80px;
                right: 20px;
                background: linear-gradient(135deg, #28a745, #20c997);
                color: white;
                padding: 20px 25px;
                border-radius: 15px;
                box-shadow: 0 8px 25px rgba(0,0,0,0.15);
                z-index: 1001;
                font-weight: bold;
                animation: slideInBounce 0.8s ease-out;
                max-width: 350px;
            `;
            welcomeDiv.innerHTML = `
                <div style="display: flex; align-items: center; gap: 12px; margin-bottom: 8px;">
                    <i class="fas fa-user-check" style="font-size: 1.2em;"></i>
                    <span style="font-size: 1.1em;">ברוך הבא ${name}!</span>
                </div>
                <div style="font-size: 0.9em; opacity: 0.9;">
                    עמוד תנאי השימוש וטופס 8832 נטען בהצלחה
                </div>
                <div style="width: 100%; height: 3px; background: rgba(255,255,255,0.3); border-radius: 2px; margin-top: 10px; overflow: hidden;">
                    <div style="width: 0%; height: 100%; background: white; border-radius: 2px; animation: progressLoad 5s ease-out;"></div>
                </div>
            `;

            document.body.appendChild(welcomeDiv);

            // Remove after 6 seconds
            setTimeout(() => {
                welcomeDiv.style.animation = 'slideOutBounce 0.5s ease-in';
                setTimeout(() => welcomeDiv.remove(), 500);
            }, 6000);
        }

        // Add enhanced animations
        const enhancedStyle = document.createElement('style');
        enhancedStyle.textContent = `
            @keyframes slideInBounce {
                0% {
                    transform: translateX(100%) scale(0.8);
                    opacity: 0;
                }
                60% {
                    transform: translateX(-10px) scale(1.05);
                    opacity: 1;
                }
                100% {
                    transform: translateX(0) scale(1);
                    opacity: 1;
                }
            }

            @keyframes slideOutBounce {
                0% {
                    transform: translateX(0) scale(1);
                    opacity: 1;
                }
                100% {
                    transform: translateX(100%) scale(0.8);
                    opacity: 0;
                }
            }

            @keyframes progressLoad {
                0% { width: 0%; }
                100% { width: 100%; }
            }
        `;
        document.head.appendChild(enhancedStyle);

        // Enhanced load personal data function
        function loadPersonalData() {
            const savedData = localStorage.getItem('taxfree_personal_data');
            if (savedData) {
                try {
                    const data = JSON.parse(savedData);
                    console.log('Loading personal data:', data);

                    // Update all client information
                    updateClientWelcome(data);

                    // Update client information in the form if elements exist
                    const clientFullName = document.getElementById('clientFullName');
                    const clientIdNumber = document.getElementById('clientIdNumber');
                    const clientPhone = document.getElementById('clientPhone');
                    const clientEmail = document.getElementById('clientEmail');

                    if (clientFullName) clientFullName.textContent = data.fullName || '[שם לא זמין]';
                    if (clientIdNumber) clientIdNumber.textContent = data.idNumber || '[ת.ז לא זמינה]';
                    if (clientPhone) clientPhone.textContent = data.phone || '[טלפון לא זמין]';
                    if (clientEmail) clientEmail.textContent = data.email || '[אימייל לא זמין]';

                    // Update signature details if elements exist
                    const signerName = document.getElementById('signerName');
                    if (signerName) signerName.textContent = data.fullName || '-';

                    // Update power of attorney form if elements exist
                    const poaClientName = document.getElementById('poa-clientName');
                    const poaClientId = document.getElementById('poa-clientId');
                    const poaClientPhone = document.getElementById('poa-clientPhone');
                    const poaClientEmail = document.getElementById('poa-clientEmail');

                    if (poaClientName) poaClientName.textContent = data.fullName || '[שם לא זמין]';
                    if (poaClientId) poaClientId.textContent = data.idNumber || '[ת.ז לא זמינה]';
                    if (poaClientPhone) poaClientPhone.textContent = data.phone || '[טלפון לא זמין]';
                    if (poaClientEmail) poaClientEmail.textContent = data.email || '[אימייל לא זמין]';

                    // Show enhanced welcome message
                    showWelcomeMessage(data.fullName);

                } catch (error) {
                    console.error('Error loading personal data:', error);
                }
            } else {
                console.log('No personal data found in localStorage');
                showNoDataMessage();
            }
        }

        // Load data when page loads
        document.addEventListener('DOMContentLoaded', function() {
            loadPersonalData();
            updateTermsProgress();

            // Add scroll spy for sections
            const sections = document.querySelectorAll('.terms-section');
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.transform = 'translateY(0)';
                        entry.target.style.opacity = '1';
                    }
                });
            }, { threshold: 0.1 });

            sections.forEach(section => {
                section.style.transform = 'translateY(20px)';
                section.style.opacity = '0.8';
                section.style.transition = 'all 0.6s ease';
                observer.observe(section);
            });
        });

        // Enhanced Accessibility Functions
        function toggleTheme() {
            const body = document.body;
            const currentTheme = body.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            body.setAttribute('data-theme', newTheme);

            const themeText = document.querySelector('.theme-text');
            if (themeText) {
                themeText.textContent = newTheme === 'dark' ? 'מצב יום' : 'מצב לילה';
            }

            localStorage.setItem('preferred-theme', newTheme);
        }

        function toggleFontSize() {
            const body = document.body;
            const currentSize = body.getAttribute('data-font-size');
            const newSize = currentSize === 'large' ? 'normal' : 'large';
            body.setAttribute('data-font-size', newSize);

            const fontText = document.querySelector('.font-text');
            if (fontText) {
                fontText.textContent = newSize === 'large' ? 'גופן רגיל' : 'גופן גדול';
            }

            localStorage.setItem('preferred-font-size', newSize);
        }

        function toggleHighContrast() {
            const body = document.body;
            const currentContrast = body.getAttribute('data-contrast');
            const newContrast = currentContrast === 'high' ? 'normal' : 'high';
            body.setAttribute('data-contrast', newContrast);

            const contrastText = document.querySelector('.contrast-text');
            if (contrastText) {
                contrastText.textContent = newContrast === 'high' ? 'ניגודיות רגילה' : 'ניגודיות גבוהה';
            }

            localStorage.setItem('preferred-contrast', newContrast);
        }

        function toggleReadingMode() {
            const body = document.body;
            const currentMode = body.getAttribute('data-reading-mode');
            const newMode = currentMode === 'true' ? 'false' : 'true';
            body.setAttribute('data-reading-mode', newMode);

            const readingText = document.querySelector('.reading-text');
            if (readingText) {
                readingText.textContent = newMode === 'true' ? 'מצב רגיל' : 'מצב קריאה';
            }

            localStorage.setItem('preferred-reading-mode', newMode);
        }

        function showHelp() {
            const modal = document.getElementById('helpModal');
            if (modal) {
                modal.style.display = 'flex';
                modal.setAttribute('aria-hidden', 'false');
                document.body.style.overflow = 'hidden';

                // Focus on first interactive element
                const firstButton = modal.querySelector('button');
                if (firstButton) firstButton.focus();
            }
        }

        function closeHelp() {
            const modal = document.getElementById('helpModal');
            if (modal) {
                modal.style.display = 'none';
                modal.setAttribute('aria-hidden', 'true');
                document.body.style.overflow = 'auto';
            }
        }

        function showHelpTab(tabName) {
            // Hide all tabs
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });
            document.querySelectorAll('.tab-btn').forEach(btn => {
                btn.classList.remove('active');
            });

            // Show selected tab
            const selectedTab = document.getElementById(tabName + '-tab');
            const selectedBtn = event.target.closest('.tab-btn');

            if (selectedTab) selectedTab.classList.add('active');
            if (selectedBtn) selectedBtn.classList.add('active');
        }

        function toggleFaq(button) {
            const faqItem = button.closest('.faq-item');
            const answer = faqItem.querySelector('.faq-answer');
            const icon = button.querySelector('i');

            const isOpen = answer.style.display === 'block';

            // Close all other FAQs
            document.querySelectorAll('.faq-answer').forEach(ans => {
                ans.style.display = 'none';
            });
            document.querySelectorAll('.faq-question i').forEach(ic => {
                ic.style.transform = 'rotate(0deg)';
            });

            // Toggle current FAQ
            if (!isOpen) {
                answer.style.display = 'block';
                icon.style.transform = 'rotate(180deg)';
            }
        }

        function startLiveChat() {
            // Simulate live chat opening
            alert('צ\'אט חי יפתח בקרוב! בינתיים ניתן ליצור קשר בטלפון: 03-1234567');
        }

        // Load user preferences on page load
        function loadUserPreferences() {
            const theme = localStorage.getItem('preferred-theme');
            const fontSize = localStorage.getItem('preferred-font-size');
            const contrast = localStorage.getItem('preferred-contrast');
            const readingMode = localStorage.getItem('preferred-reading-mode');

            if (theme) document.body.setAttribute('data-theme', theme);
            if (fontSize) document.body.setAttribute('data-font-size', fontSize);
            if (contrast) document.body.setAttribute('data-contrast', contrast);
            if (readingMode) document.body.setAttribute('data-reading-mode', readingMode);
        }

        // Enhanced keyboard navigation
        document.addEventListener('keydown', function(e) {
            // ESC to close modal
            if (e.key === 'Escape') {
                closeHelp();
            }

            // Alt + H to open help
            if (e.altKey && e.key === 'h') {
                e.preventDefault();
                showHelp();
            }

            // Alt + T to toggle theme
            if (e.altKey && e.key === 't') {
                e.preventDefault();
                toggleTheme();
            }
        });

        // Digital Signature Functionality (Form 8832 only)
        let formCanvas, formCtx;
        let isFormDrawing = false;
        let lastX = 0;
        let lastY = 0;
        let formSignatureData = null;

        function initializeSignature() {
            // Form signature canvas (8832) only
            formCanvas = document.getElementById('formSignatureCanvas');
            if (formCanvas) {
                formCtx = formCanvas.getContext('2d');
                setupCanvas(formCanvas, formCtx);
                setupCanvasEvents(formCanvas, formCtx);
            }

            // Clear button for form signature
            const clearFormBtn = document.getElementById('clearFormSignature');
            if (clearFormBtn) {
                clearFormBtn.addEventListener('click', () => clearSignature());
            }

            // Agreement checkboxes
            const checkboxes = ['agreeTerms', 'agreePowerOfAttorney', 'agreeDataProcessing', 'confirmAccuracy'];
            checkboxes.forEach(id => {
                const checkbox = document.getElementById(id);
                if (checkbox) {
                    checkbox.addEventListener('change', updateProceedButton);
                }
            });

            // Initial button state
            updateProceedButton();
        }

        function setupCanvas(canvasElement, context) {
            // Set canvas size based on attributes
            const width = parseInt(canvasElement.getAttribute('width')) || 400;
            const height = parseInt(canvasElement.getAttribute('height')) || 120;

            canvasElement.width = width;
            canvasElement.height = height;

            // Set drawing styles
            context.strokeStyle = '#2c3e50';
            context.lineWidth = 2;
            context.lineCap = 'round';
            context.lineJoin = 'round';

            // Clear canvas with white background
            context.fillStyle = '#ffffff';
            context.fillRect(0, 0, width, height);

            console.log(`Form canvas setup: ${width}x${height}`);
        }

        function setupCanvasEvents(canvasElement, context) {
            // Mouse events
            canvasElement.addEventListener('mousedown', (e) => startDrawing(e, canvasElement, context));
            canvasElement.addEventListener('mousemove', (e) => draw(e, canvasElement, context));
            canvasElement.addEventListener('mouseup', (e) => stopDrawing(e, canvasElement, context));
            canvasElement.addEventListener('mouseout', (e) => stopDrawing(e, canvasElement, context));

            // Touch events for mobile
            canvasElement.addEventListener('touchstart', (e) => handleTouch(e, canvasElement, context));
            canvasElement.addEventListener('touchmove', (e) => handleTouch(e, canvasElement, context));
            canvasElement.addEventListener('touchend', (e) => stopDrawing(e, canvasElement, context));

            // Prevent scrolling when touching the canvas
            canvasElement.addEventListener('touchstart', (e) => e.preventDefault());
            canvasElement.addEventListener('touchmove', (e) => e.preventDefault());

            console.log('Events setup for form canvas');
        }

        function getMousePos(e, canvasElement) {
            const rect = canvasElement.getBoundingClientRect();
            const scaleX = canvasElement.width / rect.width;
            const scaleY = canvasElement.height / rect.height;

            return {
                x: (e.clientX - rect.left) * scaleX,
                y: (e.clientY - rect.top) * scaleY
            };
        }

        function getTouchPos(e, canvasElement) {
            const rect = canvasElement.getBoundingClientRect();
            const scaleX = canvasElement.width / rect.width;
            const scaleY = canvasElement.height / rect.height;

            return {
                x: (e.touches[0].clientX - rect.left) * scaleX,
                y: (e.touches[0].clientY - rect.top) * scaleY
            };
        }

        function startDrawing(e, canvasElement, context) {
            isFormDrawing = true;

            const pos = getMousePos(e, canvasElement);
            lastX = pos.x;
            lastY = pos.y;

            console.log(`Start drawing at: ${pos.x}, ${pos.y}`);

            // Add active class to container
            const container = document.querySelector('.form-signature-container');
            if (container) container.classList.add('active');
        }

        function draw(e, canvasElement, context) {
            if (!isFormDrawing) return;

            e.preventDefault();
            const pos = getMousePos(e, canvasElement);

            context.beginPath();
            context.moveTo(lastX, lastY);
            context.lineTo(pos.x, pos.y);
            context.stroke();

            lastX = pos.x;
            lastY = pos.y;

            // Update status
            updateFormSignatureStatus();
        }

        function stopDrawing(e, canvasElement, context) {
            if (!isFormDrawing) return;

            isFormDrawing = false;
            formSignatureData = canvasElement.toDataURL();
            updateFormSignatureStatus();

            console.log('Stop drawing');

            // Remove active class
            const container = document.querySelector('.form-signature-container');
            if (container) container.classList.remove('active');
        }

        function handleTouch(e, canvasElement, context) {
            e.preventDefault();
            const touch = e.touches[0];
            const mouseEvent = new MouseEvent(e.type === 'touchstart' ? 'mousedown' :
                                            e.type === 'touchmove' ? 'mousemove' : 'mouseup', {
                clientX: touch.clientX,
                clientY: touch.clientY
            });

            if (e.type === 'touchstart') {
                startDrawing(mouseEvent, canvasElement, context);
            } else if (e.type === 'touchmove') {
                draw(mouseEvent, canvasElement, context);
            }
        }

        function clearSignature() {
            if (formCtx && formCanvas) {
                formCtx.fillStyle = '#ffffff';
                formCtx.fillRect(0, 0, formCanvas.width, formCanvas.height);
                formSignatureData = null;
                updateFormSignatureStatus();

                // Show clear animation
                const clearBtn = document.getElementById('clearFormSignature');
                if (clearBtn) {
                    clearBtn.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        clearBtn.style.transform = 'scale(1)';
                    }, 150);
                }
                console.log('Form signature cleared');
            }
        }

        function updateFormSignatureStatus() {
            const statusElement = document.getElementById('formSignatureStatus');
            if (!statusElement || !formCanvas) return;

            const isEmpty = isCanvasEmpty(formCanvas, formCtx);

            if (isEmpty) {
                statusElement.innerHTML = '❌ לא נחתם';
                statusElement.className = 'form-signature-status';
            } else {
                statusElement.innerHTML = '✅ נחתם';
                statusElement.className = 'form-signature-status signed';

                // Update form signature date
                const now = new Date();
                const dateElement = document.getElementById('poa-signatureDate-final');
                if (dateElement) {
                    dateElement.textContent = now.toLocaleDateString('he-IL');
                }
            }

            // Update proceed button
            updateProceedButton();

            console.log(`Form signature status: ${isEmpty ? 'empty' : 'signed'}`);
        }

        function updateProceedButton() {
            const proceedBtn = document.getElementById('proceedToCalculator');
            if (!proceedBtn) return;

            // Check all agreement checkboxes
            const checkboxes = ['agreeTerms', 'agreePowerOfAttorney', 'agreeDataProcessing', 'confirmAccuracy'];
            const allChecked = checkboxes.every(id => {
                const checkbox = document.getElementById(id);
                return checkbox && checkbox.checked;
            });

            // Check form signature
            const formSigned = formCanvas ? !isCanvasEmpty(formCanvas, formCtx) : false;

            if (allChecked && formSigned) {
                proceedBtn.disabled = false;
                proceedBtn.innerHTML = `
                    <i class="fas fa-calculator"></i>
                    המשך לחישוב מס
                    <span class="btn-subtitle">כל האישורים הושלמו ✓</span>
                `;
                proceedBtn.style.background = 'linear-gradient(135deg, #10b981, #059669)';
            } else if (allChecked && !formSigned) {
                proceedBtn.disabled = true;
                proceedBtn.innerHTML = `
                    <i class="fas fa-calculator"></i>
                    המשך לחישוב מס
                    <span class="btn-subtitle">נדרשת חתימה על טופס 8832</span>
                `;
                proceedBtn.style.background = 'linear-gradient(135deg, #6b7280, #4b5563)';
            } else if (!allChecked && formSigned) {
                proceedBtn.disabled = true;
                proceedBtn.innerHTML = `
                    <i class="fas fa-calculator"></i>
                    המשך לחישוב מס
                    <span class="btn-subtitle">נדרש סימון כל תיבות האישור</span>
                `;
                proceedBtn.style.background = 'linear-gradient(135deg, #6b7280, #4b5563)';
            } else {
                proceedBtn.disabled = true;
                proceedBtn.innerHTML = `
                    <i class="fas fa-calculator"></i>
                    המשך לחישוב מס
                    <span class="btn-subtitle">נדרש סימון כל התיבות וחתימה על טופס 8832</span>
                `;
                proceedBtn.style.background = 'linear-gradient(135deg, #6b7280, #4b5563)';
            }
        }







        function isCanvasEmpty(canvasElement, context) {
            if (!canvasElement || !context) return true;

            const imageData = context.getImageData(0, 0, canvasElement.width, canvasElement.height);
            const data = imageData.data;

            for (let i = 0; i < data.length; i += 4) {
                // Check if pixel is not white (255,255,255) and not transparent
                if (data[i] !== 255 || data[i + 1] !== 255 || data[i + 2] !== 255 || data[i + 3] !== 255) {
                    return false;
                }
            }
            return true;
        }

        function updateSignatureDetails() {
            const signerName = document.getElementById('signerName');
            const signatureDate = document.getElementById('signatureDate');
            const signatureTime = document.getElementById('signatureTime');

            const now = new Date();
            const dateStr = now.toLocaleDateString('he-IL');
            const timeStr = now.toLocaleTimeString('he-IL');

            if (signatureDate) signatureDate.textContent = dateStr;
            if (signatureTime) signatureTime.textContent = timeStr;
        }

        // Initialize preferences on load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing...');
            loadUserPreferences();

            // Wait a bit for elements to be ready
            setTimeout(() => {
                initializeSignature();

                // Debug: Check if form canvas exists
                const formCanvas = document.getElementById('formSignatureCanvas');
                console.log('Form canvas found:', !!formCanvas);

                if (formCanvas) {
                    console.log('Form canvas size:', formCanvas.width, 'x', formCanvas.height);
                }

                // Debug: Check checkboxes
                const checkboxes = ['agreeTerms', 'agreePowerOfAttorney', 'agreeDataProcessing', 'confirmAccuracy'];
                checkboxes.forEach(id => {
                    const checkbox = document.getElementById(id);
                    console.log(`Checkbox ${id} found:`, !!checkbox);
                });
            }, 100);
        });
    </script>
</body>
</html>
